<template>
  <div class="three-editor-test">

    <!-- 查询参数面板 -->
    <div class="query-panel" v-if="showQueryPanel">
      <el-form :inline="true" :model="queryParams" class="search-bar">
        <el-form-item label="工厂" :style="{width: '200px'}">
          <el-select v-model="queryParams.factoryCode" placeholder="请选择工厂" clearable @change="handleFactoryChange">
            <el-option v-for="item in factoryOptions" :key="item.code" :label="item.name" :value="item.code" />
          </el-select>
        </el-form-item>
        <el-form-item label="区域" :style="{width: '200px'}">
          <el-select v-model="queryParams.regionCode" placeholder="请选择区域" clearable @change="handleRegionChange">
            <el-option v-for="item in regionOptions" :key="item.code" :label="item.name" :value="item.code" />
          </el-select>
        </el-form-item>
        <el-form-item label="楼栋" :style="{width: '200px'}">
          <el-select
            v-model="queryParams.buildingCode"
            placeholder="请选择楼栋"
            clearable
            @change="handleBuildingChange"
            :disabled="queryParams.regionCode === '0'"
          >
            <el-option v-for="item in buildingOptions" :key="item.code" :label="item.name" :value="item.code" />
          </el-select>
        </el-form-item>
        <el-form-item label="楼层" :style="{width: '200px'}">
          <el-select
            v-model="queryParams.floorCode"
            placeholder="请选择楼层"
            clearable
            :disabled="queryParams.regionCode === '0'"
          >
            <el-option v-for="item in floorOptions" :key="item.code" :label="item.name" :value="item.code" />
          </el-select>
        </el-form-item>
        <el-form-item label="模型名称">
          <el-input v-model="queryParams.filename" placeholder="请输入模型名称" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">搜索</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 全屏3D编辑器 -->
    <div class="fullscreen-editor" :class="{ 'with-query-panel': showQueryPanel }">
      <three-editor
        ref="editorRef"
        :width="showQueryPanel ? '100vw' : '100vw'"
        :height="showQueryPanel ? 'calc(100vh - 80px)' : '100vh'"
        :visible="showEditor"
        :auto-save="true"
        :use-simple-editor="useSimpleEditor"
        :model-library-data="modelLibraryData"
        :spotlight-data="spotlightData"
        @ready="onEditorReady"
        @scene-changed="onSceneChanged"
        @object-selected="onObjectSelected"
        @object-added="onObjectAdded"
        @object-removed="onObjectRemoved"
        @save="onSave"
        @load="onLoad"
      />
    </div>

    <!-- 查询面板切换按钮 - 只在非全屏模式下显示 -->
    <div class="query-toggle-btn" v-if="route.path !== '/3d/editor/fullscreen'">
      <el-button
        :icon="showQueryPanel ? 'ArrowUp' : 'ArrowDown'"
        @click="toggleQueryPanel"
        size="small"
        type="primary"
      >
        {{ showQueryPanel ? '隐藏查询' : '显示查询' }}
      </el-button>
    </div>

    <!-- 上传对话框 -->
    <el-dialog
      v-model="showUploadDialogVisible"
      :title="uploadForm.sceneId ? '修改3D场景和模型' : '导出3D场景和模型'"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="uploadForm" label-width="120px">
        <el-form-item label="场景名称" required>
          <el-input
            v-model="uploadForm.name"
            placeholder="请输入场景名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <!-- 导出类型控件已隐藏，默认选择"场景和模型" -->
        <!-- <el-form-item label="导出类型">
          <el-radio-group v-model="uploadForm.uploadType">
            <el-radio label="both">场景和模型</el-radio>
            <el-radio label="scene">仅场景数据</el-radio>
            <el-radio label="model">仅3D模型</el-radio>
          </el-radio-group>
        </el-form-item> -->

        <!-- OBJ模型相关配置 -->
        <template v-if="uploadForm.uploadType === 'model' || uploadForm.uploadType === 'both'">
          <el-form-item label="模型类型">
            <el-radio-group v-model="uploadForm.isCommon">
              <el-radio :label="2">普通模型</el-radio>
              <el-radio :label="1">公版模型</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 公版模型显示资料夹选择 -->
          <el-form-item v-if="uploadForm.isCommon === 1" label="资料夹" required>
            <el-tree-select
              v-model="uploadForm.businessCodeGroup"
              :data="businessOptionsGroup"
              :props="{ value: 'code', label: 'name', children: 'children' }"
              value-key="code"
              placeholder="选择资料夹"
              check-strictly
              style="width: 100%"
            />
          </el-form-item>

          <!-- 工厂区域楼栋楼层选择（公版和普通模型都显示，都为非必填） -->
          <el-form-item label="工厂">
            <el-select
              v-model="uploadForm.factoryCode"
              placeholder="请选择工厂"
              @change="handleFactoryChangeUpload"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="factory in factoryOptions"
                :key="factory.code"
                :label="factory.name"
                :value="factory.code"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="区域">
            <el-select
              v-model="uploadForm.regionCode"
              placeholder="请选择区域"
              @change="handleRegionChangeUpload"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="region in regionOptions"
                :key="region.code"
                :label="region.name"
                :value="region.code"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="楼栋">
            <el-select
              v-model="uploadForm.buildingCode"
              placeholder="请选择楼栋"
              @change="handleBuildingChangeUpload"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="building in buildingOptions"
                :key="building.code"
                :label="building.name"
                :value="building.code"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="楼层">
            <el-select
              v-model="uploadForm.floorCode"
              placeholder="请选择楼层"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="floor in floorOptions"
                :key="floor.code"
                :label="floor.name"
                :value="floor.code"
              />
            </el-select>
          </el-form-item>
        </template>

        <el-form-item label="文件信息">
          <div class="upload-info">
            <div v-if="exportedData && exportedData.exportData" class="info-item">
              <el-tag type="success">场景数据: JSON格式</el-tag>
            </div>
            <div v-if="exportedData && exportedData.glbData" class="info-item">
              <el-tag type="primary">3D模型: GLB格式</el-tag>
            </div>
            <div v-if="exportedData && exportedData.stats" class="stats-info">
              <small>
                对象: {{exportedData.stats.objectCount}}个 |
                网格: {{exportedData.stats.meshCount}}个 |
                光源: {{exportedData.stats.lightCount}}个
              </small>
            </div>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleUploadCancel">取消</el-button>
          <el-button @click="handleDownloadFiles">下载文件</el-button>
          <el-button @click="debugBusinessOptions" type="info" size="small">调试业务分类</el-button>
          <el-button
            type="primary"
            @click="handleUploadSubmit"
            :loading="uploadLoading"
          >
            {{ uploadLoading ? (uploadForm.sceneId ? '修改中...' : '上传中...') : (uploadForm.sceneId ? '确认修改' : '确认上传') }}
          </el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import ThreeEditor from '@/components/Three.js/ThreeEditor.vue'
import { children, create } from '@/api/holo/astBusiness.js'
import { storeAccount } from '@/store/index.js'
import { getUserFactory } from '@/api/holo/factory'
import { getRegionList, findBuildingListWithFloor, getRegion } from '@/api/floor'
import { listSpotlight } from '@/api/holo/spotlight'
import { findPage as listSpotlighttype } from '@/api/holo/spotlighttype.js'
import { object_uploadFile } from '@/api/assets/object.js'
import { addScene, updateScene } from '@/api/holo/scene.js'
import { addModel, updateModel, getModel } from '@/api/holo/model.js'

// 响应式数据
const route = useRoute()
const editorRef = ref(null)
const showEditor = ref(true)
const isReady = ref(false)
const showPanel = ref(false)
const eventLog = ref([])
const useSimpleEditor = ref(false)

// 根据路由判断是否显示查询面板，全屏模式下隐藏查询面板
const showQueryPanel = ref(route.path !== '/3d/editor/fullscreen')

// Store实例
const _storeAccount = storeAccount()

// 上传对话框相关
const showUploadDialogVisible = ref(false)
const uploadForm = ref({
  name: '',
  businessCodeGroup: '',
  uploadType: 'both', // 'scene', 'model', 'both'
  isCommon: 2, // 模型类型：2=普通模型，1=公版模型
  factoryCode: '',
  regionCode: '',
  buildingCode: '',
  floorCode: '',
  sceneId: null, // 用于修改场景时存储场景ID
  existingModelId: null, // 用于修改模型时存储模型ID
  existingModelCode: null, // 现有的模型code
  existingSceneFileCode: null // 现有的场景文件code
})
const exportedData = ref(null)
const businessOptionsGroup = ref([])
const uploadLoading = ref(false)

// 工厂区域楼栋楼层数据
const factoryOptions = ref([])
const regionOptions = ref([])
const buildingOptions = ref([])
const floorOptions = ref([])

// 模型库数据 - 从API获取
const modelLibraryData = ref({
  systemLibrary: [],
  userLibrary: []
})

// 亮点数据 - 从API获取
const spotlightData = ref([])

// 亮点类型数据 - 用于获取iconCode和iconColor
const spotlightTypeData = ref([])

// 编辑模式下传递的数据
const editModeModelData = ref(null)
const editModeSceneData = ref(null)
const isEditModeInitialized = ref(false) // 标记是否已经初始化为编辑模式

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 100,
  businessCode: null,
  objectType: 0, // 设置默认值为0，与index.vue保持一致
  suffix: null,
  filename: null,
  factoryCode: null,
  regionCode: null,
  buildingCode: null,
  floorCode: null,
})



// 添加事件日志
const addEventLog = (message, type = 'info') => {
  const now = new Date()
  const time = now.toLocaleTimeString()
  eventLog.value.unshift({
    time,
    message,
    type
  })
  
  // 限制日志数量
  if (eventLog.value.length > 20) {
    eventLog.value = eventLog.value.slice(0, 20)
  }
}

// 编辑器事件处理
const onEditorReady = (editor) => {
  isReady.value = true
  addEventLog('编辑器已准备就绪', 'success')
  console.log('编辑器实例:', editor)

  // 检查是否有场景或模型需要加载，如果有就立即设置场景加载状态
  const { modelId, objectUrl, sceneJsonUrl } = route.query
  if (modelId && (objectUrl || sceneJsonUrl)) {
    // 立即设置场景加载状态，避免编辑器界面闪现
    if (editorRef.value && editorRef.value.setLoadingScene) {
      editorRef.value.setLoadingScene(true)
      addEventLog('准备加载场景，显示加载界面', 'info')
    }
  }

  // 检查模型库数据状态
  if (modelLibraryData.value.systemLibrary.length > 0 || modelLibraryData.value.userLibrary.length > 0) {
    addEventLog('模型库数据已准备，将通过props传递到编辑器', 'info')
  } else {
    addEventLog('模型库数据尚未加载完成，将在加载完成后自动传递', 'info')
  }
}

const onSceneChanged = (sceneData) => {
  addEventLog('场景已更改', 'info')
  console.log('场景数据:', sceneData)
}

const onObjectSelected = (object) => {
  addEventLog(`对象已选择: ${object?.name || '未命名对象'}`, 'info')
  console.log('选择的对象:', object)
}

const onObjectAdded = (object) => {
  addEventLog(`对象已添加: ${object?.name || '未命名对象'}`, 'success')
  console.log('添加的对象:', object)
}

const onObjectRemoved = (object) => {
  addEventLog(`对象已移除: ${object?.name || '未命名对象'}`, 'warning')
  console.log('移除的对象:', object)
}

const onSave = (data) => {
  console.log('🏠 父组件onSave被调用，接收到数据:', data)

  if (data && data.type === 'export') {
    // 处理导出的完整场景数据
    addEventLog('完整场景导出完成', 'success')
    console.log('🎯 父组件收到完整场景导出数据:', {
      hasExportData: !!data.exportData,
      hasGlbData: !!data.glbData,
      timestamp: data.timestamp,
      stats: data.stats
    })

    // 显示上传选择对话框
    showUploadDialog(data)
  } else {
    // 普通保存事件
    addEventLog('场景已保存', 'success')
    console.log('保存的场景数据:', data)
  }
}

// 显示上传对话框
const showUploadDialog = (data) => {
  exportedData.value = data

  // 检查是否是编辑模式（已初始化为编辑模式或者uploadForm中已经设置了sceneId）
  const isEditMode = isEditModeInitialized.value || uploadForm.value.sceneId

  console.log('=== showUploadDialog 调试信息 ===')
  console.log('isEditModeInitialized:', isEditModeInitialized.value)
  console.log('uploadForm.sceneId:', uploadForm.value.sceneId)
  console.log('最终isEditMode:', isEditMode)

  if (isEditMode) {
    // 编辑模式：使用传递的数据填充表单
    const sceneData = editModeSceneData.value
    const modelData = editModeModelData.value

    console.log('编辑模式 - sceneData:', sceneData)
    console.log('编辑模式 - modelData:', modelData)

    // 重要：不要使用缓存的ID，而是重新从数据源获取
    // 保存当前的sceneId，但不保存可能过期的modelId
    const currentSceneId = uploadForm.value.sceneId

    // 警告：不再使用缓存的existingModelId，因为它可能已经过期
    console.log('⚠️ 检测到可能的缓存ID问题')
    console.log('当前缓存的existingModelId:', uploadForm.value.existingModelId)
    console.log('将重新从数据源获取最新的modelId')

    // 如果有传递的场景数据，使用传递的数据；否则保持当前uploadForm中的值
    if (sceneData) {
      uploadForm.value = {
        name: sceneData.name || uploadForm.value.name || '未命名场景',
        businessCodeGroup: uploadForm.value.businessCodeGroup || '',
        uploadType: 'both',
        isCommon: modelData?.isCommon || uploadForm.value.isCommon || 2,
        factoryCode: sceneData.factoryCode || uploadForm.value.factoryCode || '',
        regionCode: sceneData.regionCode || uploadForm.value.regionCode || '',
        buildingCode: sceneData.buildingCode || uploadForm.value.buildingCode || '',
        floorCode: sceneData.floorCode || uploadForm.value.floorCode || '',
        sceneId: sceneData.id || currentSceneId, // 优先使用传递的ID，否则保持现有ID
        existingModelId: modelData?.id || sceneData.modelId || null, // 不使用缓存的ID，只从数据源获取
        existingModelCode: modelData?.code || sceneData.modelCode || null, // 不使用缓存的code
        existingSceneFileCode: sceneData.objCode || null
      }
      addEventLog('编辑模式：已填充传递的场景数据', 'info')
    } else {
      // 如果没有传递的场景数据，但是编辑模式，只保持sceneId，清除可能过期的modelId
      console.log('⚠️ 编辑模式但无场景数据，清除可能过期的modelId缓存')
      uploadForm.value = {
        ...uploadForm.value,
        sceneId: currentSceneId,
        existingModelId: null, // 清除可能过期的modelId
        existingModelCode: null, // 清除可能过期的modelCode
        existingSceneFileCode: null // 清除可能过期的sceneFileCode
      }
      addEventLog('编辑模式：保持sceneId，清除过期的模型ID缓存', 'info')
    }

    console.log('编辑模式 - 最终uploadForm.sceneId:', uploadForm.value.sceneId)
  } else {
    // 新增模式：生成默认数据
    const now = new Date()
    const dateStr = now.getFullYear() +
      String(now.getMonth() + 1).padStart(2, '0') +
      String(now.getDate()).padStart(2, '0')
    const timeStr = String(now.getHours()).padStart(2, '0') +
      String(now.getMinutes()).padStart(2, '0') +
      String(now.getSeconds()).padStart(2, '0')

    uploadForm.value = {
      name: `3D场景_${dateStr}_${timeStr}`,
      businessCodeGroup: '',
      uploadType: 'both',
      isCommon: 2,
      factoryCode: '',
      regionCode: '',
      buildingCode: '',
      floorCode: '',
      sceneId: null, // 确保新增操作时sceneId为null
      existingModelId: null,
      existingModelCode: null,
      existingSceneFileCode: null
    }

    addEventLog('新增模式：已生成默认场景数据', 'info')
  }

  showUploadDialogVisible.value = true

  // 调试日志：显示最终的sceneId值
  console.log('showUploadDialog - 最终sceneId:', uploadForm.value.sceneId)
  console.log('showUploadDialog - 操作模式:', uploadForm.value.sceneId ? '修改' : '新增')
  addEventLog(`操作模式: ${uploadForm.value.sceneId ? '修改' : '新增'}`, 'info')

  // 加载业务分类数据和工厂数据
  loadBusinessOptions()
  loadFactoryOptions()

  // 如果是编辑模式且有工厂代码，加载对应的区域数据
  if (isEditMode && uploadForm.value.factoryCode) {
    handleFactoryChangeUpload(uploadForm.value.factoryCode).then(() => {
      if (uploadForm.value.regionCode) {
        handleRegionChangeUpload(uploadForm.value.regionCode).then(() => {
          if (uploadForm.value.buildingCode) {
            handleBuildingChangeUpload(uploadForm.value.buildingCode)
          }
        })
      }
    })
  }
}

// 加载业务分类选项
const loadBusinessOptions = async () => {
  try {
    // 检查组织代码是否存在
    if (!_storeAccount.organizeCode) {
      console.error('组织代码为空，无法加载业务分类')
      ElMessage.error('组织代码为空，请先登录')
      return
    }

    // 参考baseModel的实现，使用组织代码构建key
    const key = _storeAccount.organizeCode + '_3d_businessModel'
    console.log('正在加载业务分类，key:', key)
    console.log('组织代码:', _storeAccount.organizeCode)

    const response = await children(key)
    console.log('children API响应:', response)

    // 处理不同的响应格式
    let data = null
    if (response && response.data) {
      data = response.data
    } else if (response && Array.isArray(response)) {
      data = response
    } else {
      console.warn('未知的响应格式:', response)
      data = []
    }

    console.log('业务分类数据:', data)

    if (Array.isArray(data) && data.length === 0) {
      // 如果没有数据，创建一个默认的组织模型库
      try {
        const createResponse = await create({
          parentCode: -1,
          key: key,
          name: '组织模型库'
        })
        console.log('创建组织模型库成功:', createResponse)
        businessOptionsGroup.value = [
          {
            code: key,
            name: '全部',
            children: []
          }
        ]
      } catch (createError) {
        console.error('创建组织模型库失败:', createError)
        // 即使创建失败，也提供一个默认选项
        businessOptionsGroup.value = [
          {
            code: key,
            name: '全部',
            children: []
          }
        ]
      }
    } else if (Array.isArray(data) && data.length > 0) {
      // 使用返回的数据
      businessOptionsGroup.value = [
        {
          code: data[0].code,
          name: '全部',
          children: data[0].children || [],
          objectList: data[0].objectList || []
        }
      ]
    } else {
      // 数据格式不正确或为空
      businessOptionsGroup.value = []
    }
  } catch (error) {
    console.error('加载业务分类失败:', error)
    ElMessage.error('加载业务分类失败: ' + (error.message || '未知错误'))
    // 设置默认值以防止界面报错
    businessOptionsGroup.value = []
  }
}

// 加载工厂选项
const loadFactoryOptions = async () => {
  try {
    const response = await getUserFactory()
    if (response && response.data) {
      factoryOptions.value = response.data
    }
  } catch (error) {
    console.error('加载工厂数据失败:', error)
    ElMessage.error('加载工厂数据失败')
  }
}

// 处理工厂选择变化（上传对话框）
const handleFactoryChangeUpload = async (factoryCode) => {
  uploadForm.value.regionCode = ''
  uploadForm.value.buildingCode = ''
  uploadForm.value.floorCode = ''
  regionOptions.value = []
  buildingOptions.value = []
  floorOptions.value = []

  if (factoryCode) {
    try {
      const response = await getRegionList({ factoryCode })
      if (response && response.data) {
        regionOptions.value = response.data
      }
    } catch (error) {
      console.error('加载区域数据失败:', error)
      ElMessage.error('加载区域数据失败')
    }
  }
}

// 处理区域选择变化（上传对话框）
const handleRegionChangeUpload = async (regionCode) => {
  uploadForm.value.buildingCode = ''
  uploadForm.value.floorCode = ''
  buildingOptions.value = []
  floorOptions.value = []

  if (regionCode) {
    try {
      const response = await findBuildingListWithFloor({ regionCode })
      if (response && response.data) {
        buildingOptions.value = response.data
      }
    } catch (error) {
      console.error('加载楼栋数据失败:', error)
      ElMessage.error('加载楼栋数据失败')
    }
  }
}

// 处理楼栋选择变化（上传对话框）
const handleBuildingChangeUpload = (buildingCode) => {
  uploadForm.value.floorCode = ''

  if (buildingCode) {
    const selectedBuilding = buildingOptions.value.find(b => b.code === buildingCode)
    if (selectedBuilding && selectedBuilding.floors) {
      floorOptions.value = selectedBuilding.floors
    }
  } else {
    floorOptions.value = []
  }
}

// 处理上传提交
const handleUploadSubmit = async () => {
  // 基础验证
  if (!uploadForm.value.name.trim()) {
    ElMessage.error('请输入场景名称')
    return
  }

  if (!exportedData.value) {
    ElMessage.error('没有可上传的数据')
    return
  }

  // 验证模型相关字段
  if (uploadForm.value.uploadType === 'model' || uploadForm.value.uploadType === 'both') {
    if (uploadForm.value.isCommon === 1) {
      // 公版模型只验证资料夹
      if (!uploadForm.value.businessCodeGroup) {
        ElMessage.error('请选择资料夹')
        return
      }
    }
    // 工厂区域楼栋楼层对所有模型类型都为非必填，不需要验证
  }

  uploadLoading.value = true

  try {
    let modelCode = uploadForm.value.existingModelCode || null
    let sceneFileCode = uploadForm.value.existingSceneFileCode || null
    let modelId = null // 用于存储model记录的ID

    // 先上传GLB模型文件（如果需要且有新数据）
    if ((uploadForm.value.uploadType === 'model' || uploadForm.value.uploadType === 'both') && exportedData.value.glbData) {
      const modelResult = await uploadModelData()
      if (modelResult && modelResult.data) {
        modelCode = modelResult.data.code
        console.log('模型文件上传成功，获得code:', modelCode)

        // 调用model.js的addModel方法创建模型记录
        const modelRecord = await createModelRecord(modelResult.data)
        if (modelRecord && modelRecord.data) {
          modelId = modelRecord.data.id
          // 重要：使用addModel返回的code，而不是文件上传的code
          modelCode = modelRecord.data.code
          console.log('模型记录创建成功，获得ID:', modelId)
          console.log('模型记录创建成功，获得code:', modelCode)
        }
      }
    }

    // 上传场景JSON文件到文件系统（如果需要且有新数据）
    if ((uploadForm.value.uploadType === 'scene' || uploadForm.value.uploadType === 'both') && exportedData.value.exportData) {
      const sceneFileResult = await uploadSceneFile()
      if (sceneFileResult && sceneFileResult.data) {
        sceneFileCode = sceneFileResult.data.code || sceneFileResult.data.id
        console.log('场景文件上传成功，获得code:', sceneFileCode)
      }

      // 确保在修改模式下有正确的modelCode
      if (uploadForm.value.sceneId && !modelCode) {
        console.warn('修改模式下缺少modelCode，尝试从编辑数据中获取')
        if (editModeModelData.value && editModeModelData.value.code) {
          modelCode = editModeModelData.value.code
          console.log('从编辑数据中获取到modelCode:', modelCode)
        }
      }

      console.log('最终传递给uploadSceneData的参数:')
      console.log('  modelCode:', modelCode)
      console.log('  sceneFileCode:', sceneFileCode)
      console.log('  操作类型:', uploadForm.value.sceneId ? '修改' : '新增')

      // 创建或更新场景记录（使用holo/scene.js的API）
      await uploadSceneData(modelCode, sceneFileCode)
    }

    ElMessage.success(uploadForm.value.sceneId ? '修改成功' : '上传成功')
    showUploadDialogVisible.value = false
    addEventLog(uploadForm.value.sceneId ? '场景和模型修改成功' : '场景和模型上传成功', 'success')

  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error('操作失败: ' + (error.message || '未知错误'))
    addEventLog(uploadForm.value.sceneId ? '场景和模型修改失败' : '场景和模型上传失败', 'error')
  } finally {
    uploadLoading.value = false
  }
}

// 上传场景文件到文件系统
const uploadSceneFile = async () => {
  console.log('=== 开始上传场景JSON文件 ===')
  console.log('场景数据信息:', {
    hasExportData: !!exportedData.value.exportData,
    dataKeys: exportedData.value.exportData ? Object.keys(exportedData.value.exportData) : []
  })

  // 使用Three.js编辑器的标准格式，而不是自定义格式
  let sceneDataToSave = exportedData.value.exportData

  // 如果是自定义格式，需要获取Three.js编辑器的标准格式
  if (exportedData.value.exportData && exportedData.value.exportData.camera && Array.isArray(exportedData.value.exportData.camera.position)) {
    console.log('检测到自定义格式，尝试获取Three.js编辑器标准格式...')

    // 尝试从编辑器获取标准格式
    if (editorRef.value && editorRef.value.getSceneData) {
      const standardFormat = editorRef.value.getSceneData()
      if (standardFormat && standardFormat.project) {
        console.log('使用Three.js编辑器标准格式保存')
        sceneDataToSave = standardFormat
      }
    }
  }

  const jsonString = JSON.stringify(sceneDataToSave, null, 2)
  const jsonBlob = new Blob([jsonString], { type: 'application/json' })
  console.log('创建的JSON Blob信息:', {
    size: jsonBlob.size,
    type: jsonBlob.type,
    jsonLength: jsonString.length
  })

  const formData = new FormData()
  const fileName = `${uploadForm.value.name}_scene.json`
  formData.append('file', jsonBlob, fileName)
  console.log('场景文件名:', fileName)

  // 根据模型类型选择businessCode（与模型文件保持一致）
  let businessCode
  // 场景文件上传：无论公版还是普通模型都使用相同的固定businessCode
  businessCode = 'd_bus_zyDWojyJ3BIv_4R'
  formData.append('businessCode', businessCode)
  console.log('场景文件 - businessCode:', businessCode)

  const remark = `${uploadForm.value.name} - 3D场景配置文件`
  formData.append('remark', remark)
  console.log('场景文件 - remark:', remark)

  // 打印FormData内容（用于调试）
  console.log('场景文件FormData内容:')
  for (let [key, value] of formData.entries()) {
    if (key === 'file') {
      console.log(`  ${key}:`, {
        name: value.name,
        size: value.size,
        type: value.type
      })
    } else {
      console.log(`  ${key}:`, value)
    }
  }

  try {
    console.log('开始调用 object_uploadFile API (场景文件)...')
    const response = await object_uploadFile(formData)

    console.log('=== 场景JSON文件上传成功 ===')
    console.log('完整响应数据:', response)
    console.log('响应状态码:', response.code)
    console.log('响应消息:', response.msg || response.message)

    if (response.data) {
      console.log('场景文件响应数据详情:')
      console.log('  文件code:', response.data.code)
      console.log('  文件ID:', response.data.id)
      console.log('  文件名:', response.data.filename)
      console.log('  文件后缀:', response.data.suffix)
      console.log('  文件大小:', response.data.fileSize)
      console.log('  本地URL:', response.data.localUrl)
      console.log('  OSS URL:', response.data.ossUrl)
      console.log('  业务代码:', response.data.businessCode)
      console.log('  备注信息:', response.data.remark)
      console.log('  创建时间:', response.data.createTime)
      console.log('  更新时间:', response.data.updateTime)
    }

    return response
  } catch (error) {
    console.error('=== 场景JSON文件上传失败 ===')
    console.error('错误信息:', error)
    console.error('错误详情:', error.response || error.message)
    throw error
  }
}

// 创建模型记录（使用holo/model.js的API）
const createModelRecord = async (uploadFileData) => {
  console.log('=== 开始创建模型记录 ===')
  console.log('传入的文件上传数据:', uploadFileData)
  console.log('传入的uploadForm:', uploadForm.value)

  // 根据isCommon设置modelType参数
  let modelType
  if (uploadForm.value.isCommon === 1) {
    // 公版模型：使用固定值
    modelType = 'd_bus_zyDWojyJ3BIv_4R'
  } else {
    // 普通模型：使用固定值
    modelType = 'd_bus_zyDWojyJ3BIv_4R'
  }

  console.log('设置modelType参数:', modelType, '(isCommon:', uploadForm.value.isCommon, ')')

  const modelData = {
    name: uploadForm.value.name,
    objCode: uploadFileData.code, // 使用上传后获得的模型文件code
    isCommon: uploadForm.value.isCommon, // 模型类型：2=普通模型，1=公版模型
    modelType: modelType, // 根据isCommon设置的modelType参数
    factoryCode: uploadForm.value.factoryCode || null,
    regionCode: uploadForm.value.regionCode || null,
    buildingCode: uploadForm.value.buildingCode || null,
    floorCode: uploadForm.value.floorCode || null,
    remark: `${uploadForm.value.name} - 3D模型记录`,
    suffix: uploadFileData.suffix, // 使用上传文件返回的后缀
    filename: uploadFileData.filename, // 使用上传文件返回的文件名
    fileSize: uploadFileData.fileSize, // 使用上传文件返回的文件大小
    localUrl: uploadFileData.localUrl, // 使用上传文件返回的本地URL
    ossUrl: uploadFileData.ossUrl, // 使用上传文件返回的OSS URL
    businessCode: uploadFileData.businessCode // 使用上传文件返回的业务代码
  }

  console.log('模型记录数据:', modelData)
  console.log('模型记录数据中的modelType:', modelData.modelType)

  try {
    let response
    // 如果有现有的模型ID，执行更新操作
    if (uploadForm.value.existingModelId) {
      modelData.id = uploadForm.value.existingModelId
      console.log('=== 准备执行模型记录修改操作 ===')
      console.log('模型ID:', uploadForm.value.existingModelId)
      console.log('模型ID类型:', typeof uploadForm.value.existingModelId)
      console.log('完整的modelData:', JSON.stringify(modelData, null, 2))
      console.log('修改操作中的modelType:', modelData.modelType)

      // 在调用updateModel之前，先验证ID是否存在
      try {
        console.log('正在验证模型ID是否存在...')
        const existingModel = await getModel(uploadForm.value.existingModelId)
        console.log('模型ID验证成功，现有模型数据:', existingModel)
      } catch (verifyError) {
        console.error('⚠️ 模型ID验证失败，该ID在数据库中不存在:', verifyError)
        console.error('错误详情:', verifyError.response || verifyError.message)

        // 提供更友好的错误处理
        const errorMsg = `模型ID ${uploadForm.value.existingModelId} 在数据库中不存在`
        console.log('🔄 自动切换为新增模式...')
        addEventLog('模型ID不存在，自动切换为新增模式', 'warning')

        // 清除无效的ID，切换为新增模式
        uploadForm.value.existingModelId = null
        uploadForm.value.existingModelCode = null

        // 重新调用新增逻辑
        console.log('执行模型记录新增操作（从更新模式自动切换）')
        console.log('新增操作中的modelType:', modelData.modelType)
        response = await addModel(modelData)
        console.log('=== 模型记录新增成功（自动切换模式）===')

        ElMessage.warning(errorMsg + '，已自动切换为新增模式')
      }

      response = await updateModel(modelData)
      console.log('=== 模型记录修改成功 ===')
    } else {
      console.log('执行模型记录新增操作')
      console.log('新增操作中的modelType:', modelData.modelType)
      response = await addModel(modelData)
      console.log('=== 模型记录新增成功 ===')
    }

    console.log('模型记录操作响应数据:', response)
    console.log('响应状态码:', response.code)
    console.log('响应消息:', response.msg || response.message)

    if (response.data) {
      console.log('模型记录详情:')
      console.log('  模型ID:', response.data.id)
      console.log('  模型名称:', response.data.name)
      console.log('  对象代码:', response.data.objCode)
      console.log('  工厂代码:', response.data.factoryCode)
      console.log('  区域代码:', response.data.regionCode)
      console.log('  楼栋代码:', response.data.buildingCode)
      console.log('  楼层代码:', response.data.floorCode)
      console.log('  备注信息:', response.data.remark)
      console.log('  创建时间:', response.data.createTime)
      console.log('  更新时间:', response.data.updateTime)
    }

    return response
  } catch (error) {
    console.error('=== 模型记录操作失败 ===')
    console.error('错误信息:', error)
    console.error('错误详情:', error.response || error.message)
    throw error
  }
}

// 上传场景数据（使用holo/scene.js的API）
const uploadSceneData = async (modelCode, sceneFileCode) => {
  console.log('=== 开始创建/更新场景记录 ===')
  console.log('传入参数:')
  console.log('  modelCode:', modelCode)
  console.log('  sceneFileCode:', sceneFileCode)
  console.log('  sceneId:', uploadForm.value.sceneId)
  console.log('  isUpdate:', !!uploadForm.value.sceneId)

  let sceneData = {}

  // 如果有sceneId，说明是修改操作
  if (uploadForm.value.sceneId) {
    // 修改操作：只修改objCode，不修改modelCode
    sceneData = {
      id: uploadForm.value.sceneId,
      name: uploadForm.value.name,
      objCode: sceneFileCode, // 只修改场景文件的code
      remark: `${uploadForm.value.name} - 3D场景配置`
      // 注意：不包含modelCode，保持原有的modelCode不变
    }
    console.log('执行修改操作，场景ID:', uploadForm.value.sceneId)
    console.log('修改数据（不包含modelCode）:', sceneData)
  } else {
    // 新增操作：需要包含modelCode
    sceneData = {
      name: uploadForm.value.name,
      modelCode: modelCode, // 新增时需要关联模型code
      objCode: sceneFileCode, // 场景文件上传返回的code
      remark: `${uploadForm.value.name} - 3D场景配置`
    }
    console.log('执行新增操作')
    console.log('新增数据（包含modelCode）:', sceneData)
  }

  console.log('场景数据对象:', sceneData)

  try {
    let response
    if (uploadForm.value.sceneId) {
      response = await updateScene(sceneData)
      console.log('=== 场景修改成功 ===')
    } else {
      response = await addScene(sceneData)
      console.log('=== 场景新增成功 ===')
    }

    console.log('场景操作响应数据:', response)
    console.log('响应状态码:', response.code)
    console.log('响应消息:', response.msg || response.message)

    if (response.data) {
      console.log('场景记录详情:')
      console.log('  场景ID:', response.data.id)
      console.log('  场景名称:', response.data.name)
      console.log('  模型代码:', response.data.modelCode)
      console.log('  场景文件代码:', response.data.sceneFileCode)
      console.log('  备注信息:', response.data.remark)
      console.log('  创建时间:', response.data.createTime)
      console.log('  更新时间:', response.data.updateTime)
    }

    return response
  } catch (error) {
    console.error('=== 场景记录操作失败 ===')
    console.error('错误信息:', error)
    console.error('错误详情:', error.response || error.message)
    throw error
  }
}

// 上传模型数据（使用baseModel的方式，与baseModel保持完全一致）
const uploadModelData = async () => {
  console.log('=== 开始上传GLB模型文件 ===')
  console.log('上传表单数据:', uploadForm.value)
  console.log('导出数据信息:', {
    hasGlbData: !!exportedData.value.glbData,
    glbDataSize: exportedData.value.glbData ? exportedData.value.glbData.byteLength : 0
  })

  const modelBlob = new Blob([exportedData.value.glbData], { type: 'application/octet-stream' })
  console.log('创建的Blob信息:', {
    size: modelBlob.size,
    type: modelBlob.type
  })

  const formData = new FormData()
  const fileName = `${uploadForm.value.name}_model.glb`
  formData.append('file', modelBlob, fileName)
  console.log('文件名:', fileName)

  if (uploadForm.value.isCommon === 1) {
    // 公版模型：根据选择的资料夹来设置businessCode
    const businessCode = uploadForm.value.businessCodeGroup
    formData.append('businessCode', businessCode)
    console.log('公版模型 - businessCode:', businessCode)

    // 如果公版模型也选择了工厂区域楼栋楼层，将这些信息添加到备注中
    let remark = uploadForm.value.name // 与baseModel一致，使用name作为remark
    const locationInfo = []
    if (uploadForm.value.factoryCode) locationInfo.push(`工厂:${uploadForm.value.factoryCode}`)
    if (uploadForm.value.regionCode) locationInfo.push(`区域:${uploadForm.value.regionCode}`)
    if (uploadForm.value.buildingCode) locationInfo.push(`楼栋:${uploadForm.value.buildingCode}`)
    if (uploadForm.value.floorCode) locationInfo.push(`楼层:${uploadForm.value.floorCode}`)

    if (locationInfo.length > 0) {
      remark += ` [${locationInfo.join(', ')}]`
    }

    formData.append('remark', remark)
    console.log('公版模型 - remark:', remark)
  } else {
    // 普通模型：统一使用固定的businessCode
    const businessCode = 'd_bus_8HNUX2QF63hlWpO'
    formData.append('businessCode', businessCode)
    console.log('普通模型 - businessCode:', businessCode)

    let remark = uploadForm.value.name // 与baseModel一致，使用name作为remark
    const locationInfo = []
    if (uploadForm.value.factoryCode) locationInfo.push(`工厂:${uploadForm.value.factoryCode}`)
    if (uploadForm.value.regionCode) locationInfo.push(`区域:${uploadForm.value.regionCode}`)
    if (uploadForm.value.buildingCode) locationInfo.push(`楼栋:${uploadForm.value.buildingCode}`)
    if (uploadForm.value.floorCode) locationInfo.push(`楼层:${uploadForm.value.floorCode}`)

    if (locationInfo.length > 0) {
      remark += ` [${locationInfo.join(', ')}]`
    }

    formData.append('remark', remark)
    console.log('普通模型 - remark:', remark)
  }

  // 打印FormData内容（用于调试）
  console.log('FormData内容:')
  for (let [key, value] of formData.entries()) {
    if (key === 'file') {
      console.log(`  ${key}:`, {
        name: value.name,
        size: value.size,
        type: value.type
      })
    } else {
      console.log(`  ${key}:`, value)
    }
  }

  try {
    console.log('开始调用 object_uploadFile API...')
    const response = await object_uploadFile(formData)

    console.log('=== GLB模型文件上传成功 ===')
    console.log('完整响应数据:', response)
    console.log('响应状态码:', response.code)
    console.log('响应消息:', response.msg || response.message)

    if (response.data) {
      console.log('响应数据详情:')
      console.log('  文件code:', response.data.code)
      console.log('  文件ID:', response.data.id)
      console.log('  文件名:', response.data.filename)
      console.log('  文件后缀:', response.data.suffix)
      console.log('  文件大小:', response.data.fileSize)
      console.log('  本地URL:', response.data.localUrl)
      console.log('  OSS URL:', response.data.ossUrl)
      console.log('  业务代码:', response.data.businessCode)
      console.log('  备注信息:', response.data.remark)
      console.log('  创建时间:', response.data.createTime)
      console.log('  更新时间:', response.data.updateTime)
    }

    return response
  } catch (error) {
    console.error('=== GLB模型文件上传失败 ===')
    console.error('错误信息:', error)
    console.error('错误详情:', error.response || error.message)
    throw error
  }
}

// 下载文件
const handleDownloadFiles = () => {
  if (!exportedData.value) {
    ElMessage.error('没有可下载的数据')
    return
  }

  // 生成用户友好的文件名
  const now = new Date()
  const dateStr = now.getFullYear() +
    String(now.getMonth() + 1).padStart(2, '0') +
    String(now.getDate()).padStart(2, '0')
  const timeStr = String(now.getHours()).padStart(2, '0') +
    String(now.getMinutes()).padStart(2, '0') +
    String(now.getSeconds()).padStart(2, '0')

  const sceneFilename = `${uploadForm.value.name || '3d_scene'}_${dateStr}_${timeStr}.json`
  const modelFilename = `${uploadForm.value.name || '3d_model'}_${dateStr}_${timeStr}.glb`

  // 下载完整场景JSON文件
  if ((uploadForm.value.uploadType === 'scene' || uploadForm.value.uploadType === 'both') && exportedData.value.exportData) {
    const jsonString = JSON.stringify(exportedData.value.exportData, null, 2)
    downloadFile(jsonString, sceneFilename, 'application/json')
  }

  // 下载GLB文件
  if ((uploadForm.value.uploadType === 'model' || uploadForm.value.uploadType === 'both') && exportedData.value.glbData) {
    downloadFile(exportedData.value.glbData, modelFilename, 'application/octet-stream')
  }

  addEventLog('文件下载完成', 'success')
  ElMessage.success('文件下载完成')
}

// 调试业务分类
const debugBusinessOptions = () => {
  console.log('=== 业务分类调试信息 ===')
  console.log('组织代码:', _storeAccount.organizeCode)
  console.log('用户代码:', _storeAccount.userCode)
  console.log('构建的Key:', _storeAccount.organizeCode + '_3d_businessModel')
  console.log('当前业务分类数据:', businessOptionsGroup.value)
  console.log('Store完整信息:', _storeAccount)

  ElMessage.info('调试信息已输出到控制台，请按F12查看')

  // 重新加载业务分类
  loadBusinessOptions()
}

// 支持修改功能：显示编辑场景对话框
const showEditSceneDialog = (sceneData = null, exportData = null) => {
  // 如果没有传递参数，使用编辑模式下的数据
  const actualSceneData = sceneData || editModeSceneData.value
  const actualModelData = editModeModelData.value

  if (!actualSceneData) {
    ElMessage.warning('没有可编辑的场景数据')
    return
  }

  // 如果没有导出数据，先导出当前场景
  if (!exportData) {
    handleExportAndUpload()
    return
  }

  exportedData.value = exportData

  // 填充现有场景数据
  uploadForm.value = {
    name: actualSceneData.name || '',
    businessCodeGroup: actualSceneData.businessCode || '',
    uploadType: 'both',
    isCommon: actualModelData?.isCommon || 2,
    factoryCode: actualSceneData.factoryCode || '',
    regionCode: actualSceneData.regionCode || '',
    buildingCode: actualSceneData.buildingCode || '',
    floorCode: actualSceneData.floorCode || '',
    sceneId: actualSceneData.id, // 设置场景ID用于修改
    existingModelId: actualModelData?.id || actualSceneData.modelId, // 现有的模型ID
    existingModelCode: actualModelData?.code || actualSceneData.modelCode, // 现有的模型code
    existingSceneFileCode: actualSceneData.objCode // 现有的场景文件code
  }

  // 调试日志：显示existingModelId的来源
  console.log('=== existingModelId 设置调试信息 ===')
  console.log('actualModelData?.id:', actualModelData?.id)
  console.log('actualSceneData.modelId:', actualSceneData.modelId)
  console.log('最终设置的existingModelId:', actualModelData?.id || actualSceneData.modelId)
  console.log('existingModelId类型:', typeof (actualModelData?.id || actualSceneData.modelId))
  console.log('actualModelData完整数据:', actualModelData)
  console.log('actualSceneData完整数据:', actualSceneData)

  showUploadDialogVisible.value = true

  // 加载相关数据
  loadBusinessOptions()
  loadFactoryOptions()

  // 如果有工厂代码，加载对应的区域数据
  if (actualSceneData.factoryCode) {
    handleFactoryChangeUpload(actualSceneData.factoryCode).then(() => {
      if (actualSceneData.regionCode) {
        handleRegionChangeUpload(actualSceneData.regionCode).then(() => {
          if (actualSceneData.buildingCode) {
            handleBuildingChangeUpload(actualSceneData.buildingCode)
          }
        })
      }
    })
  }

  addEventLog('已填充现有场景数据用于修改', 'info')
}

// 取消上传
const handleUploadCancel = () => {
  showUploadDialogVisible.value = false
  exportedData.value = null
  uploadForm.value = {
    name: '',
    businessCodeGroup: '',
    uploadType: 'both',
    isCommon: 2,
    factoryCode: '',
    regionCode: '',
    buildingCode: '',
    floorCode: '',
    sceneId: null,
    existingModelId: null,
    existingModelCode: null,
    existingSceneFileCode: null
  }

  // 清空选项数据
  regionOptions.value = []
  buildingOptions.value = []
  floorOptions.value = []
}

const onLoad = (sceneData) => {
  addEventLog('场景已加载', 'success')
  console.log('加载的场景数据:', sceneData)
}

// 辅助函数：下载文件
const downloadFile = (data, filename, mimeType) => {
  try {
    console.log(`📥 开始下载文件: ${filename}, 类型: ${mimeType}`)
    console.log(`📊 数据大小: ${data ? (typeof data === 'string' ? data.length : data.byteLength || data.size || 'unknown') : 'null'} bytes`)

    const blob = new Blob([data], { type: mimeType })
    console.log(`📦 Blob创建成功, 大小: ${blob.size} bytes`)

    const url = URL.createObjectURL(blob)
    console.log(`🔗 URL创建成功: ${url}`)

    const link = document.createElement('a')
    link.href = url
    link.download = filename
    link.style.display = 'none'

    console.log(`🔗 下载链接创建: href=${link.href}, download=${link.download}`)

    document.body.appendChild(link)
    console.log(`📎 链接已添加到DOM`)

    link.click()
    console.log(`👆 链接已点击，触发下载`)

    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    console.log(`✅ 文件下载已触发: ${filename}`)

    addEventLog(`文件下载成功: ${filename}`, 'success')
  } catch (error) {
    console.error(`❌ 文件下载失败: ${filename}`, error)
    addEventLog(`文件下载失败: ${filename}`, 'error')
  }
}

// 辅助函数：格式化时间戳
const formatTimestamp = (timestamp) => {
  const date = new Date(timestamp)
  return date.toISOString().replace(/[:.]/g, '-').slice(0, -5)
}

// 面板控制方法
const togglePanel = () => {
  showPanel.value = !showPanel.value
  addEventLog(`控制面板${showPanel.value ? '打开' : '关闭'}`, 'info')
}

// 查询面板控制方法
const toggleQueryPanel = () => {
  showQueryPanel.value = !showQueryPanel.value
  addEventLog(`查询面板${showQueryPanel.value ? '显示' : '隐藏'}`, 'info')
}

// 测试方法
const toggleEditor = () => {
  showEditor.value = !showEditor.value
  addEventLog(`编辑器${showEditor.value ? '显示' : '隐藏'}`, 'info')
}

const toggleEditorType = () => {
  useSimpleEditor.value = !useSimpleEditor.value
  addEventLog(`切换到${useSimpleEditor.value ? '简化版' : '完整版'}编辑器`, 'info')
  ElMessage.info(`已切换到${useSimpleEditor.value ? '简化版' : '完整版'}编辑器，请刷新页面生效`)
}

const getSceneInfo = () => {
  if (!isReady.value) {
    ElMessage.warning('编辑器尚未准备就绪')
    return
  }
  
  try {
    const sceneData = editorRef.value?.getSceneData()
    if (sceneData) {
      addEventLog('获取场景信息成功', 'success')
      console.log('当前场景数据:', sceneData)
      ElMessage.success('场景信息已输出到控制台')
    } else {
      addEventLog('获取场景信息失败', 'error')
      ElMessage.error('无法获取场景信息')
    }
  } catch (error) {
    addEventLog(`获取场景信息出错: ${error.message}`, 'error')
    ElMessage.error('获取场景信息时发生错误')
    console.error('获取场景信息错误:', error)
  }
}

const clearScene = () => {
  if (!isReady.value) {
    ElMessage.warning('编辑器尚未准备就绪')
    return
  }
  
  try {
    editorRef.value?.clearScene()
    addEventLog('场景已清空', 'warning')
    ElMessage.success('场景已清空')
  } catch (error) {
    addEventLog(`清空场景出错: ${error.message}`, 'error')
    ElMessage.error('清空场景时发生错误')
    console.error('清空场景错误:', error)
  }
}

const loadTestScene = () => {
  if (!isReady.value) {
    ElMessage.warning('编辑器尚未准备就绪')
    return
  }
  
  // 创建一个简单的测试场景数据
  const testScene = {
    metadata: {
      version: 4.5,
      type: "Object",
      generator: "Object3D.toJSON"
    },
    object: {
      uuid: "test-scene-uuid",
      type: "Scene",
      name: "TestScene",
      children: [
        {
          uuid: "test-cube-uuid",
          type: "Mesh",
          name: "TestCube",
          geometry: "BoxGeometry",
          material: "MeshBasicMaterial",
          position: [0, 0, 0],
          rotation: [0, 0, 0],
          scale: [1, 1, 1]
        }
      ]
    }
  }
  
  try {
    editorRef.value?.loadScene(testScene)
    addEventLog('测试场景已加载', 'success')
    ElMessage.success('测试场景加载成功')
  } catch (error) {
    addEventLog(`加载测试场景出错: ${error.message}`, 'error')
    ElMessage.error('加载测试场景时发生错误')
    console.error('加载测试场景错误:', error)
  }
}

// API调用函数


/**
 * 获取系统模型库文件夹 - 支持多层结构，与用户模型库使用相同的数据处理逻辑
 */
const loadSystemModelLibrary = async () => {
  try {
    const response = await children('3d_model_system')
    const { data } = response

    console.log('系统模型库API响应:', response)
    console.log('系统模型库数据:', data)

    if (data && data.length > 0) {
      // 获取根节点数据
      const rootFolder = data[0]

      // 保留所有文件夹层级，包括空文件夹
      if (rootFolder.children) {
        // 使用objectFileBuild处理，保留所有层级结构
        const systemLibrary = objectFileBuild(rootFolder.children)
        modelLibraryData.value.systemLibrary = systemLibrary
        addEventLog(`系统模型库加载成功，共${systemLibrary.length}个主分类`, 'success')
        console.log('处理后的系统模型库数据:', systemLibrary)
      } else {
        // 即使根节点没有children，也要保留根节点结构
        const systemLibrary = objectFileBuild([rootFolder])
        modelLibraryData.value.systemLibrary = systemLibrary
        addEventLog('系统模型库只有根节点', 'info')
      }
    } else {
      modelLibraryData.value.systemLibrary = []
      addEventLog('系统模型库数据为空', 'warning')
    }
  } catch (error) {
    console.error('加载系统模型库失败:', error)
    addEventLog('加载系统模型库失败，使用默认数据', 'warning')
    // 使用默认数据
    modelLibraryData.value.systemLibrary = [
      {
        code: 'furniture',
        name: '家具',
        children: [
          {
            code: 'shelf-a',
            name: 'shelf-a.glb',
            filename: 'shelf-a',
            suffix: 'glb',
            localUrl: 'models/furniture/shelf-a.glb',
            type: 'file'
          },
          {
            code: 'shelf-b',
            name: 'shelf-b.glb',
            filename: 'shelf-b',
            suffix: 'glb',
            localUrl: 'models/furniture/shelf-b.glb',
            type: 'file'
          }
        ]
      }
    ]
  }
}

/**
 * 参考index.vue的objectFileBuild函数，处理文件夹和文件的数据结构
 * 保留所有文件夹层级，包括空文件夹
 */
const objectFileBuild = (arr) => {
  // 深拷贝数据，防止修改原数组
  const dataArr = JSON.parse(JSON.stringify(arr))

  return dataArr.map((item) => {
    // 初始化 children
    item.children = item.children || []

    // 将 objectList 转为子节点
    if (Array.isArray(item.objectList)) {
      const files = item.objectList.map((file) => {
        file.name = file.filename + '.' + file.suffix
        file.type = 'file'
        return file
      })
      item.children.push(...files)
    }

    // 递归处理 children（保留所有子项，包括空文件夹）
    if (item.children.length > 0) {
      item.children = objectFileBuild(item.children)
    }

    // 即使没有children也要保留该项（保持文件夹层级结构）
    return item
  })
}

/**
 * 获取用户模型库文件夹 - 完全参考index.vue的loadBusinessOptionsGroup实现
 */
const loadUserModelLibrary = async () => {
  try {
    // 使用与index.vue相同的key格式
    const key = _storeAccount.organizeCode + '_3d_businessModel'
    const response = await children(key)
    const { data } = response

    console.log('用户模型库API响应:', response)
    console.log('用户模型库数据:', data)

    if (data && data.length === 0) {
      // 如果没有数据，创建一个全部的模型库（参考index.vue的逻辑）
      try {
        await create({
          parentCode: -1,
          key: key,
          name: '组织模型库',
        })

        // 创建后设置默认结构
        modelLibraryData.value.userLibrary = [
          {
            code: key,
            name: '全部',
            children: [],
          }
        ]

        addEventLog('已创建组织模型库根节点', 'info')
      } catch (createError) {
        console.error('创建组织模型库失败:', createError)
        addEventLog('创建组织模型库失败', 'error')
        modelLibraryData.value.userLibrary = []
      }
    } else if (data && data.length > 0) {
      // 完全参考index.vue的数据处理逻辑，只保留"全部"这一个顶级分类
      const rootData = data[0]

      // 构建业务选项组，只包含"全部"分类，但保留完整的子文件夹层级
      const businessOptionsGroup = [
        {
          code: rootData.code,
          name: '全部',
          children: rootData.children || [], // 保留所有子文件夹，即使为空
          objectList: rootData.objectList || [], // 保留根级别的文件
        }
      ]

      // 使用与index.vue相同的objectFileBuild函数处理数据
      const businessFileOptionsGroup = objectFileBuild(businessOptionsGroup)

      console.log('处理后的用户模型库数据结构:', businessFileOptionsGroup)
      console.log('原始数据结构:', data[0])

      // 设置用户模型库数据
      modelLibraryData.value.userLibrary = businessFileOptionsGroup

      addEventLog(`用户模型库加载成功，共${businessFileOptionsGroup.length}个分类`, 'success')
    } else {
      modelLibraryData.value.userLibrary = []
      addEventLog('用户模型库数据为空', 'warning')
    }

  } catch (error) {
    console.error('加载用户模型库失败:', error)
    addEventLog(`加载用户模型库失败: ${error.message}`, 'error')
    // 设置为空数组，将显示空状态
    modelLibraryData.value.userLibrary = []
  }
}





/**
 * 加载亮点类型数据
 */
const loadSpotlightTypeData = async () => {
  try {
    addEventLog('正在加载亮点类型数据...', 'info')

    const response = await listSpotlighttype({ pageNum: 1, pageSize: 100 })
    console.log('亮点类型API响应:', response)

    if (response.code === 200 && response.data) {
      spotlightTypeData.value = response.data.list || []
      addEventLog(`亮点类型数据加载成功，共${spotlightTypeData.value.length}个类型`, 'success')
      console.log('亮点类型数据:', spotlightTypeData.value)

      // 调试：检查每个亮点类型的图标信息
      spotlightTypeData.value.forEach(type => {
        console.log(`亮点类型 "${type.nameI18n?.zhContent || type.name}" 的图标:`, {
          code: type.code,
          iconCode: type.iconCode,
          iconColor: type.iconColor
        })

        // 检查对应的SVG文件是否存在
        const svgSymbol = document.querySelector(`#icon-${type.iconCode}`)
        if (svgSymbol) {
          console.log(`✅ SVG符号存在: #icon-${type.iconCode}`)
        } else {
          console.warn(`⚠️ SVG符号不存在: #icon-${type.iconCode}`)
        }
      })
    } else {
      addEventLog('亮点类型数据加载失败', 'error')
    }
  } catch (error) {
    console.error('加载亮点类型数据失败:', error)
    addEventLog('加载亮点类型数据失败', 'error')
  }
}

/**
 * 加载亮点数据
 */
const loadSpotlightData = async () => {
  try {
    addEventLog('正在加载亮点数据...', 'info')

    // 构建查询参数，基于当前的查询条件
    const params = {
      pageNum: 1,
      pageSize: 100,
      factoryCode: queryParams.value.factoryCode,
      regionCode: queryParams.value.regionCode,
      buildingCode: queryParams.value.buildingCode,
      floorCode: queryParams.value.floorCode
    }

    // 移除空值参数
    Object.keys(params).forEach(key => {
      if (!params[key]) {
        delete params[key]
      }
    })

    console.log('亮点查询参数:', params)
    const response = await listSpotlight(params)
    console.log('亮点API响应:', response)

    if (response.code === 200 && response.data) {
      const rawSpotlightData = response.data.list || []

      // 合并亮点类型的iconCode和iconColor信息
      spotlightData.value = rawSpotlightData.map(spotlight => {
        const spotlightType = spotlightTypeData.value.find(type => type.code === spotlight.typeCode)
        const mergedData = {
          ...spotlight,
          iconCode: spotlightType?.iconCode || 'button',
          iconColor: spotlightType?.iconColor || '#b2c2c2'
        }

        // 调试信息：打印每个亮点的图标信息
        console.log(`亮点 "${spotlight.name}" 的图标信息:`, {
          typeCode: spotlight.typeCode,
          iconCode: mergedData.iconCode,
          iconColor: mergedData.iconColor,
          spotlightType: spotlightType
        })

        return mergedData
      })

      addEventLog(`亮点数据加载成功，共${spotlightData.value.length}个亮点`, 'success')
      console.log('合并后的亮点数据:', spotlightData.value)

      // 如果没有亮点数据，添加一些测试数据用于调试
      if (spotlightData.value.length === 0) {
        console.log('🧪 没有亮点数据，添加测试数据用于调试图标显示')
        spotlightData.value = [
          {
            code: 'test-1',
            name: '测试亮点1',
            nameI18n: { zhContent: '测试亮点1' },
            typeCode: 'test-type-1',
            iconCode: 'button',
            iconColor: '#409eff'
          },
          {
            code: 'test-2',
            name: '测试亮点2',
            nameI18n: { zhContent: '测试亮点2' },
            typeCode: 'test-type-2',
            iconCode: 'star',
            iconColor: '#67c23a'
          },
          {
            code: 'test-3',
            name: '测试亮点3',
            nameI18n: { zhContent: '测试亮点3' },
            typeCode: 'test-type-3',
            iconCode: 'monitor',
            iconColor: '#e6a23c'
          },
          {
            code: 'test-4',
            name: '测试亮点4',
            nameI18n: { zhContent: '测试亮点4' },
            typeCode: 'test-type-4',
            iconCode: 'nonexistent-icon', // 不存在的图标，用于测试备用方案
            iconColor: '#f56c6c'
          }
        ]
        addEventLog('已添加测试亮点数据用于调试', 'info')
      }
    } else {
      addEventLog('亮点数据加载失败', 'error')
    }
  } catch (error) {
    console.error('加载亮点数据失败:', error)
    addEventLog('加载亮点数据失败', 'error')
  }
}

// 组件挂载时的初始化
onMounted(async () => {
  addEventLog('全屏3D编辑器测试页面已加载', 'info')

  // 检查URL参数，判断是否是编辑模式
  const { modelId, modelName, objectUrl, objCode, sceneJsonUrl, loadScene, modelData, sceneData, editMode } = route.query

  // 解析传递过来的数据
  let parsedModelData = null
  let parsedSceneData = null

  try {
    if (modelData) {
      parsedModelData = JSON.parse(decodeURIComponent(modelData))
      console.log('接收到的模型数据:', parsedModelData)
      addEventLog(`已解析模型数据: ${parsedModelData.name}`, 'info')
    }
    if (sceneData) {
      parsedSceneData = JSON.parse(decodeURIComponent(sceneData))
      console.log('接收到的场景数据:', parsedSceneData)
      addEventLog(`已解析场景数据: ${parsedSceneData.name} (ID: ${parsedSceneData.id})`, 'info')
    }
  } catch (error) {
    console.error('解析传递的数据失败:', error)
    addEventLog('解析传递的数据失败', 'error')
  }

  if (modelId && (objectUrl || sceneJsonUrl)) {
    // 编辑模式：从URL参数中获取模型信息
    addEventLog(`正在加载现有模型进行编辑: ${modelName || 'unnamed'}`, 'info')

    // 设置上传表单的初始值，用于后续保存
    uploadForm.value = {
      ...uploadForm.value,
      name: modelName || '未命名模型',
      existingModelId: modelId,
      existingModelCode: objCode,
      // 如果有场景数据，设置场景相关信息用于修改功能
      sceneId: parsedSceneData?.id || null,
      existingSceneFileCode: parsedSceneData?.objCode || null
    }

    // 调试日志：显示从URL参数设置的existingModelId
    console.log('=== 从URL参数设置existingModelId ===')
    console.log('URL参数中的modelId:', modelId)
    console.log('modelId类型:', typeof modelId)
    console.log('设置后的existingModelId:', uploadForm.value.existingModelId)
    console.log('existingModelId类型:', typeof uploadForm.value.existingModelId)

    // 验证URL参数中的modelId是否有效
    if (modelId) {
      console.log('正在验证URL参数中的modelId是否在数据库中存在...')
      try {
        const verifyResult = await getModel(modelId)
        console.log('✅ URL参数中的modelId验证成功:', verifyResult)
        addEventLog(`模型ID ${modelId} 验证成功`, 'success')
      } catch (verifyError) {
        console.error('❌ URL参数中的modelId验证失败，该ID在数据库中不存在:', verifyError)
        console.error('错误详情:', verifyError.response || verifyError.message)
        addEventLog(`模型ID ${modelId} 在数据库中不存在，将清除该ID`, 'error')

        // 清除无效的modelId
        uploadForm.value.existingModelId = null
        uploadForm.value.existingModelCode = null

        ElMessage.warning(`模型ID ${modelId} 在数据库中不存在，已切换为新增模式`)
      }
    }

    console.log('初始化uploadForm，sceneId设置为:', parsedSceneData?.id)
    console.log('当前uploadForm.sceneId:', uploadForm.value.sceneId)
    addEventLog(`初始化表单，sceneId: ${uploadForm.value.sceneId}`, 'info')

    // 如果是编辑模式，存储模型和场景数据供后续使用
    if (editMode === 'true') {
      // 将数据存储到组件的响应式变量中，供修改功能使用
      if (parsedModelData) {
        editModeModelData.value = parsedModelData
        addEventLog('已接收模型数据，可用于修改功能', 'info')
      }
      if (parsedSceneData) {
        editModeSceneData.value = parsedSceneData
        addEventLog('已接收场景数据，可用于修改功能', 'info')
      }
      isEditModeInitialized.value = true // 标记已初始化为编辑模式
    }

    // 在编辑器准备好后，加载模型或场景
    const checkEditorReady = setInterval(async () => {
      if (isReady.value && editorRef.value) {
        clearInterval(checkEditorReady)

        try {
          // 优先加载场景JSON文件
          if (loadScene === 'true' && sceneJsonUrl) {
            addEventLog('正在加载场景JSON文件...', 'info')

            try {
              // 使用编辑器的loadSceneFromUrl方法加载完整场景
              await editorRef.value.loadSceneFromUrl(sceneJsonUrl)
              addEventLog(`场景已加载: ${modelName || 'unnamed'}`, 'success')
              ElMessage.success('3D编辑器加载成功！')
            } catch (sceneError) {
              console.error('场景加载失败，尝试加载原始模型:', sceneError)
              addEventLog(`场景加载失败: ${sceneError.message}`, 'error')

              // 如果场景加载失败，尝试加载原始模型
              if (objectUrl) {
                addEventLog('正在尝试加载原始模型文件...', 'info')

                // 检查objectUrl是否已经包含完整路径，避免重复拼接
                let modelUrl = objectUrl
                if (!objectUrl.startsWith('http://') && !objectUrl.startsWith('https://')) {
                  modelUrl = `/platform-api/${objectUrl}`
                }
                console.log('场景加载失败时使用的模型URL:', modelUrl)

                const response = await fetch(modelUrl)
                const blob = await response.blob()

                // 创建File对象
                const file = new File([blob], `${modelName || 'model'}.glb`, { type: 'model/gltf-binary' })

                // 使用importFile方法加载模型
                editorRef.value.importFile(file)
                addEventLog(`原始模型已加载: ${modelName || 'unnamed'}`, 'success')
                ElMessage.success('3D编辑器加载成功！')
              } else {
                addEventLog('无法加载场景或模型文件', 'error')
              }
            }

          } else if (objectUrl) {
            // 加载单个模型文件
            addEventLog('正在加载模型文件...', 'info')

            // 检查objectUrl是否已经包含完整路径，避免重复拼接
            let modelUrl = objectUrl
            if (!objectUrl.startsWith('http://') && !objectUrl.startsWith('https://')) {
              modelUrl = `/platform-api/${objectUrl}`
            }
            console.log('直接加载模型时使用的URL:', modelUrl)

            const response = await fetch(modelUrl)
            const blob = await response.blob()

            // 创建File对象
            const file = new File([blob], `${modelName || 'model'}.glb`, { type: 'model/gltf-binary' })

            // 使用importFile方法加载模型
            editorRef.value.importFile(file)
            addEventLog(`模型已加载: ${modelName || 'unnamed'}`, 'success')
            ElMessage.success('3D编辑器加载成功！')
          }
        } catch (error) {
          console.error('加载失败:', error)
          addEventLog(`加载失败: ${error.message}`, 'error')
        }
      }
    }, 500)
  } else {
    addEventLog('正在加载模型库数据...', 'info')
  }

  // 先加载亮点类型数据，再加载其他数据
  await loadSpotlightTypeData()

  // 加载模型库数据和亮点数据
  await Promise.all([
    loadSystemModelLibrary(),
    loadUserModelLibrary(),
    loadSpotlightData()
  ])

  addEventLog('模型库数据加载完成', 'success')
  addEventLog('点击右上角按钮打开控制面板', 'info')

  // 如果没有场景或模型需要加载，在数据加载完成后显示成功提示
  if (!modelId && !sceneJsonUrl && !objectUrl) {
    ElMessage.success('3D编辑器加载成功！')
  }

  // 3秒后自动显示控制面板提示
  setTimeout(() => {
    if (!showPanel.value) {
      addEventLog('提示: 点击右上角齿轮图标打开控制面板', 'info')
    }
  }, 3000)
})

// 监听模型库数据变化，记录状态
watch(
  () => modelLibraryData.value,
  (newData) => {
    if (newData.systemLibrary.length > 0 || newData.userLibrary.length > 0) {
      addEventLog('模型库数据已更新，将自动传递到编辑器', 'info')
    }
  },
  { deep: true }
)

// 查询相关的数据和函数（已在上面定义，这里删除重复声明）

// 查询相关函数
const handleQuery = () => {
  addEventLog('执行搜索查询', 'info')
  // 重新加载亮点数据
  if (isReady.value) {
    loadSpotlightData()
  }
}

const resetQuery = () => {
  queryParams.value = {
    pageNum: 1,
    pageSize: 100,
    businessCode: null,
    objectType: 0,
    suffix: null,
    filename: null,
    factoryCode: null,
    regionCode: null,
    buildingCode: null,
    floorCode: null,
  }
  addEventLog('重置查询条件', 'info')
  handleQuery()
}

const handleFactoryChange = () => {
  queryParams.value.regionCode = null
  queryParams.value.buildingCode = null
  queryParams.value.floorCode = null
  addEventLog('工厂选择已更改', 'info')
  // 这里可以添加加载区域数据的逻辑
}

const handleRegionChange = () => {
  queryParams.value.buildingCode = null
  queryParams.value.floorCode = null
  addEventLog('区域选择已更改', 'info')
  // 这里可以添加加载楼栋数据的逻辑
}

const handleBuildingChange = () => {
  queryParams.value.floorCode = null
  addEventLog('楼栋选择已更改', 'info')
  // 这里可以添加加载楼层数据的逻辑
}

// 监听查询参数变化，重新加载亮点数据
watch(() => queryParams.value, async () => {
  if (isReady.value) {
    // 先加载亮点类型数据，再加载亮点数据
    await loadSpotlightTypeData()
    await loadSpotlightData()
  }
}, { deep: true })
</script>

<style scoped>
.three-editor-test {
  position: relative;
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.fullscreen-editor {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

/* 浮动控制面板 */
.floating-panel {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 350px;
  max-height: 80vh;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 1000;
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: rgba(64, 158, 255, 0.1);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  font-weight: 600;
  color: #303133;
}

.panel-content {
  padding: 20px;
  max-height: calc(80vh - 60px);
  overflow-y: auto;
}

.control-section,
.status-section,
.log-section {
  margin-bottom: 20px;
}

.control-section h4,
.status-section h4,
.log-section h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #606266;
  font-weight: 600;
}

.control-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-buttons .el-button {
  width: 100%;
  justify-content: flex-start;
}

.status-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 6px;
  font-size: 12px;
}

.status-item .label {
  color: #909399;
  font-weight: 500;
}

.status-item .value {
  color: #606266;
  font-weight: 600;
}

.status-item .value.ready {
  color: #67c23a;
}

/* 浮动切换按钮 */
.toggle-button {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  background: rgba(64, 158, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1000;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);
}

.toggle-button:hover {
  background: rgba(64, 158, 255, 1);
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
}

.toggle-button .el-icon {
  color: white;
  font-size: 20px;
}

/* 事件日志样式 */
.event-log {
  max-height: 150px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 11px;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 6px;
  padding: 8px;
}

.event-item {
  display: flex;
  margin-bottom: 4px;
  padding: 4px 6px;
  border-radius: 3px;
  line-height: 1.4;
}

.event-item.success {
  background-color: rgba(103, 194, 58, 0.1);
  color: #529b2e;
}

.event-item.warning {
  background-color: rgba(230, 162, 60, 0.1);
  color: #b88230;
}

.event-item.error {
  background-color: rgba(245, 108, 108, 0.1);
  color: #c45656;
}

.event-item.info {
  background-color: rgba(144, 147, 153, 0.1);
  color: #73767a;
}

.event-time {
  font-weight: 600;
  margin-right: 8px;
  min-width: 60px;
  font-size: 10px;
}

.event-message {
  flex: 1;
  word-break: break-word;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .floating-panel {
    top: 10px;
    right: 10px;
    left: 10px;
    width: auto;
    max-height: 70vh;
  }

  .toggle-button {
    top: 10px;
    right: 10px;
    width: 45px;
    height: 45px;
  }

  .panel-content {
    padding: 15px;
  }

  .control-buttons {
    gap: 6px;
  }
}

/* 滚动条样式 */
.event-log::-webkit-scrollbar,
.panel-content::-webkit-scrollbar {
  width: 4px;
}

.event-log::-webkit-scrollbar-track,
.panel-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
}

.event-log::-webkit-scrollbar-thumb,
.panel-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 2px;
}

.event-log::-webkit-scrollbar-thumb:hover,
.panel-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}

/* 查询面板切换按钮样式 */
.query-toggle-btn {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 1000;
}

/* 查询面板样式 */
.query-panel {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 999;
  padding: 20px;
}

.search-bar {
  margin: 0;
}

/* 全屏模式下隐藏查询相关元素 */
.three-editor-test.fullscreen-mode .query-panel,
.three-editor-test.fullscreen-mode .query-toggle-btn {
  display: none !important;
}

/* 上传对话框样式 */
.upload-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  align-items: center;
}

.stats-info {
  margin-top: 8px;
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  color: #606266;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
