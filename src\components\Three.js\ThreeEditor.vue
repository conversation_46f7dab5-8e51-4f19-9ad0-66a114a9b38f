<template>
  <div class="three-editor-container">
    <div ref="editorContainer" class="editor-wrapper" :style="containerStyle">
      <!-- 直接使用原始Three.js编辑器 -->
      <iframe
        ref="editorFrame"
        :src="editorUrl"
        class="editor-iframe"
        @load="onEditorLoad"
        frameborder="0"
        allowfullscreen
      ></iframe>

      <!-- 加载状态覆盖层 -->
      <div v-if="!isReady || isLoadingScene" class="loading-overlay">
        <div class="loading-content">
          <div class="loading-spinner"></div>
          <p v-if="!isReady">正在加载Three.js编辑器...</p>
          <p v-else-if="isLoadingScene">正在加载场景...</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'

// Props定义
const props = defineProps({
  width: {
    type: [String, Number],
    default: '100%'
  },
  height: {
    type: [String, Number],
    default: '600px'
  },
  visible: {
    type: Boolean,
    default: true
  },
  autoSave: {
    type: Boolean,
    default: true
  },
  initialScene: {
    type: Object,
    default: null
  },
  useSimpleEditor: {
    type: Boolean,
    default: false
  },
  modelLibraryData: {
    type: Object,
    default: () => ({
      systemLibrary: [],
      userLibrary: []
    })
  },
  spotlightData: {
    type: Array,
    default: () => []
  }
})

// Emits定义
const emit = defineEmits([
  'ready',
  'sceneChanged',
  'objectSelected',
  'objectAdded',
  'objectRemoved',
  'save',
  'load'
])

// 响应式数据
const editorContainer = ref(null)
const editorFrame = ref(null)
const isReady = ref(false)
const isLoadingScene = ref(false)

// 编辑器实例
let editor = null

// 计算属性
const containerStyle = computed(() => ({
  width: typeof props.width === 'number' ? `${props.width}px` : props.width,
  height: typeof props.height === 'number' ? `${props.height}px` : props.height,
  display: props.visible ? 'block' : 'none'
}))

const editorUrl = computed(() => {
  // 根据props选择编辑器版本
  if (props.useSimpleEditor) {
    return '/three-editor-simple.html'
  }
  // 使用完整版编辑器
  return '/three-editor/editor/index.html'
})

// iframe加载完成处理
const onEditorLoad = () => {
  console.log('iframe加载完成，正在初始化编辑器...')

  // 使用多次重试机制
  let retryCount = 0
  const maxRetries = 5

  const tryInitEditor = () => {
    try {
      const iframe = editorFrame.value
      if (iframe && iframe.contentWindow) {
        // 获取iframe中的编辑器实例
        editor = iframe.contentWindow.editor

        if (editor) {
          setupEditorEvents()

          // 注入SVG符号到iframe中
          injectSvgSymbols(iframe)

          isReady.value = true
          emit('ready', editor)

          // 延迟传递模型库数据到编辑器，确保ResourcePanel已初始化
          setTimeout(() => {
            if (props.modelLibraryData && iframe.contentWindow.setModelLibraryData) {
              iframe.contentWindow.setModelLibraryData(props.modelLibraryData)
              console.log('模型库数据已传递到编辑器:', props.modelLibraryData)
            }

            // 传递亮点数据到编辑器
            if (props.spotlightData && props.spotlightData.length > 0 && iframe.contentWindow.setSpotlightData) {
              iframe.contentWindow.setSpotlightData(props.spotlightData)
              console.log('✅ 亮点数据已传递到编辑器:', props.spotlightData)
            }
          }, 1000)

          // 如果有初始场景，加载它
          if (props.initialScene) {
            loadScene(props.initialScene)
          }

          console.log('✅ Three.js编辑器已成功加载')
          return true
        } else {
          console.log(`⏳ 编辑器实例尚未准备就绪 (尝试 ${retryCount + 1}/${maxRetries})`)
          return false
        }
      }
    } catch (error) {
      console.warn('访问编辑器时出现错误:', error.message)
      return false
    }
    return false
  }

  // 立即尝试一次
  if (tryInitEditor()) {
    return
  }

  // 如果失败，使用递增延迟重试
  const retryWithDelay = () => {
    retryCount++
    if (retryCount >= maxRetries) {
      console.warn('⚠️ 达到最大重试次数，编辑器可能无法完全初始化')
      console.log('💡 编辑器界面仍然可用，但API功能可能受限')
      isReady.value = true
      emit('ready', null)
      return
    }

    const delay = Math.min(1000 * retryCount, 5000) // 递增延迟，最大5秒
    setTimeout(() => {
      if (!tryInitEditor()) {
        retryWithDelay()
      }
    }, delay)
  }

  retryWithDelay()
}

// 注入SVG符号到iframe中
const injectSvgSymbols = (iframe) => {
  try {
    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document

    // 查找所有可能的SVG符号容器
    const svgContainers = [
      document.querySelector('svg[style*="display: none"]'),
      document.querySelector('svg[style*="display:none"]'),
      document.querySelector('#__svg__icons__dom__'),
      document.querySelector('svg[data-svg-icons]'),
      document.body.querySelector('svg:first-child')
    ]

    let symbolsFound = false

    for (const container of svgContainers) {
      if (container && container.innerHTML.includes('<symbol')) {
        // 克隆SVG符号并注入到iframe中
        const clonedSymbols = container.cloneNode(true)
        clonedSymbols.style.display = 'none'
        clonedSymbols.style.position = 'absolute'

        // 将SVG符号添加到iframe的body开头
        if (iframeDoc.body) {
          iframeDoc.body.insertBefore(clonedSymbols, iframeDoc.body.firstChild)
          console.log('✅ SVG符号已注入到编辑器iframe中', container)

          // 调试：检查注入的符号数量
          const symbols = clonedSymbols.querySelectorAll('symbol')
          console.log(`📊 注入了 ${symbols.length} 个SVG符号:`, Array.from(symbols).map(s => s.id))

          symbolsFound = true
          break
        }
      }
    }

    if (!symbolsFound) {
      console.warn('⚠️ 未找到父页面中的SVG符号容器')
      // 打印所有SVG元素用于调试
      const allSvgs = document.querySelectorAll('svg')
      console.log('父页面中的所有SVG元素:', allSvgs)
    }
  } catch (error) {
    console.error('❌ 注入SVG符号失败:', error)
  }
}

// 设置编辑器事件监听
const setupEditorEvents = () => {
  if (!editor || !editor.signals) return

  try {
    const signals = editor.signals

    // 监听场景变化
    if (signals.sceneGraphChanged) {
      signals.sceneGraphChanged.add(() => {
        emit('sceneChanged', getSceneData())
      })
    }

    // 监听对象选择
    if (signals.objectSelected) {
      signals.objectSelected.add((object) => {
        emit('objectSelected', object)
      })
    }

    // 监听对象添加
    if (signals.objectAdded) {
      signals.objectAdded.add((object) => {
        emit('objectAdded', object)
      })
    }

    // 监听对象移除
    if (signals.objectRemoved) {
      signals.objectRemoved.add((object) => {
        emit('objectRemoved', object)
      })
    }

    // 监听保存事件
    if (signals.savingFinished) {
      signals.savingFinished.add(() => {
        emit('save', getSceneData())
      })
    }

    // 监听自定义保存事件（用于导出完整场景和GLB）
    // 创建一个全局的保存事件监听器，通过window对象通信
    window.addEventListener('customSaveEvent', (event) => {
      console.log('🎯 ThreeEditor组件收到完整场景导出事件:', event.detail)
      console.log('📤 准备通过emit传递给父组件...')

      const saveData = {
        type: 'export',
        exportData: event.detail.exportData,
        glbData: event.detail.glbData,
        timestamp: event.detail.timestamp,
        stats: event.detail.stats
      };

      console.log('📦 传递给父组件的数据:', {
        type: saveData.type,
        hasExportData: !!saveData.exportData,
        hasGlbData: !!saveData.glbData,
        timestamp: saveData.timestamp,
        stats: saveData.stats
      });

      emit('save', saveData)
      console.log('✅ 数据已通过emit传递给父组件')
    })

    // 同时监听postMessage事件（备用方案）
    window.addEventListener('message', (event) => {
      if (event.data && event.data.type === 'customSaveEvent') {
        console.log('📨 通过postMessage收到完整场景导出事件:', event.data.detail)
        console.log('📤 准备通过emit传递给父组件...')

        const saveData = {
          type: 'export',
          exportData: event.data.detail.exportData,
          glbData: event.data.detail.glbData,
          timestamp: event.data.detail.timestamp,
          stats: event.data.detail.stats
        };

        console.log('📦 传递给父组件的数据:', {
          type: saveData.type,
          hasExportData: !!saveData.exportData,
          hasGlbData: !!saveData.glbData,
          timestamp: saveData.timestamp,
          stats: saveData.stats
        });

        emit('save', saveData)
        console.log('✅ 数据已通过emit传递给父组件（来自postMessage）')
      }
    })

    console.log('✅ 完整场景导出事件监听已设置（window事件 + postMessage）')

    console.log('编辑器事件监听已设置')
  } catch (error) {
    console.error('设置编辑器事件监听失败:', error)
  }
}

// 公共API方法
const getSceneData = () => {
  if (!editor) return null
  try {
    return editor.toJSON()
  } catch (error) {
    console.error('获取场景数据失败:', error)
    return null
  }
}

const loadScene = async (sceneData) => {
  if (!editor || !sceneData) return

  try {
    // 如果还没有设置加载状态，则设置它
    if (!isLoadingScene.value) {
      isLoadingScene.value = true
    }
    console.log('loadScene被调用，数据类型检查:', {
      hasProject: !!sceneData.project,
      hasScene: !!sceneData.scene,
      hasCamera: !!sceneData.camera,
      hasMetadata: !!sceneData.metadata,
      cameraHasPosition: sceneData.camera && Array.isArray(sceneData.camera.position)
    })

    // 如果是Three.js编辑器标准格式，直接使用fromJSON
    if (sceneData.project && sceneData.scene && sceneData.camera && sceneData.metadata) {
      console.log('使用Three.js编辑器标准格式直接加载')
      await editor.fromJSON(sceneData)
      emit('load', sceneData)
      return
    }

    // 如果是自定义格式，需要转换
    if (sceneData.camera && Array.isArray(sceneData.camera.position) && sceneData.scene) {
      console.log('检测到自定义格式，尝试转换为标准格式')

      // 构建Three.js编辑器期望的标准格式
      // 关键问题：需要正确转换相机数据格式
      const cameraData = {
        metadata: {
          version: 4.7,
          type: 'Object',
          generator: 'Object3D.toJSON'
        },
        object: {
          uuid: 'camera-uuid-' + Date.now(),
          type: sceneData.camera.type || 'PerspectiveCamera',
          name: 'Camera',
          layers: 1,
          matrix: [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0,
                   sceneData.camera.position[0],
                   sceneData.camera.position[1],
                   sceneData.camera.position[2], 1],
          up: [0, 1, 0],
          fov: sceneData.camera.fov || 50,
          zoom: sceneData.camera.zoom || 1,
          near: sceneData.camera.near || 0.1,
          far: sceneData.camera.far || 2000,
          focus: 10,
          aspect: 1.7777777777777777,
          filmGauge: 35,
          filmOffset: 0
        }
      }

      const standardFormat = {
        metadata: sceneData.metadata || {},
        project: {
          shadows: true,
          shadowType: 1,
          toneMapping: 0,
          toneMappingExposure: 1
        },
        camera: cameraData, // 使用正确转换的相机数据
        scene: sceneData.scene, // 使用原始场景数据
        scripts: sceneData.scripts || {},
        history: sceneData.history || { undos: [], redos: [] },
        environment: sceneData.environment || null
      }

      console.log('转换后的标准格式:', standardFormat)
      await editor.fromJSON(standardFormat)
      emit('load', standardFormat)
      return
    }

    // 如果都不匹配，抛出错误
    throw new Error('不支持的场景数据格式')

  } catch (error) {
    console.error('场景加载失败:', error)
    console.error('原始场景数据:', sceneData)
    throw error
  } finally {
    isLoadingScene.value = false
  }
}

// 处理和转换场景数据格式
const processSceneData = (sceneData) => {
  console.log('开始处理场景数据:', sceneData)

  // 检查是否已经是Three.js编辑器标准格式（包含project字段）
  if (sceneData.project && sceneData.scene && sceneData.camera && sceneData.metadata) {
    console.log('检测到Three.js编辑器标准格式，直接使用...')
    return sceneData
  }

  // 检查是否是自定义导出格式（包含camera.position等字段）
  if (sceneData.camera && Array.isArray(sceneData.camera.position)) {
    console.log('检测到自定义导出格式，尝试直接使用原始scene数据...')

    // 检查原始scene数据的结构
    console.log('原始scene数据:', sceneData.scene)
    console.log('原始scene.object:', sceneData.scene?.object)
    console.log('原始scene.metadata:', sceneData.scene?.metadata)

    // 如果原始scene数据已经是正确的格式，直接使用
    if (sceneData.scene && sceneData.scene.object && sceneData.scene.object.type) {
      console.log('原始scene数据格式正确，直接使用')

      // 修复场景数据中可能缺少type字段的对象
      console.log('开始修复场景数据...')
      const fixedSceneData = fixSceneObjects(sceneData.scene)
      console.log('场景数据修复完成:', fixedSceneData)

      // 只转换相机数据，其他数据使用修复后的数据
      const processedData = {
        metadata: sceneData.metadata || {},
        project: {
          shadows: true,
          shadowType: 1,
          toneMapping: 0,
          toneMappingExposure: 1
        },
        camera: sceneData.camera, // 尝试直接使用原始相机数据
        scene: fixedSceneData,    // 使用修复后的scene数据
        scripts: sceneData.scripts || {},
        history: sceneData.history || {
          undos: [],
          redos: []
        },
        environment: sceneData.environment || null
      }

      console.log('最终处理的数据:', processedData)
      return processedData
    } else {
      console.error('原始scene数据格式不正确:', sceneData.scene)
      throw new Error('场景数据格式不正确，无法加载')
    }
  }

  // 如果数据已经是标准格式，直接返回
  if (sceneData.metadata && sceneData.scene && sceneData.scene.object && sceneData.scene.object.type) {
    console.log('场景数据已经是标准格式')
    return sceneData
  }

  console.log('使用默认格式处理')
  // 默认处理逻辑
  const generateUUID = () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0
      const v = c === 'x' ? r : (r & 0x3 | 0x8)
      return v.toString(16)
    })
  }

  const processedData = {
    metadata: sceneData.metadata || {
      version: 4.5,
      type: 'Object',
      generator: 'Object3D.toJSON'
    },
    scene: {
      metadata: {
        version: 4.5,
        type: 'Object',
        generator: 'Object3D.toJSON'
      },
      object: sceneData.scene?.object || sceneData.object || {
        uuid: generateUUID(),
        type: 'Scene',
        name: 'Scene',
        layers: 1,
        matrix: [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1],
        children: []
      }
    },
    camera: sceneData.camera || {
      metadata: {
        version: 4.5,
        type: 'Object',
        generator: 'Object3D.toJSON'
      },
      object: {
        uuid: generateUUID(),
        type: 'PerspectiveCamera',
        name: 'Camera',
        layers: 1,
        matrix: [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1],
        fov: 50,
        zoom: 1,
        near: 0.1,
        far: 2000,
        focus: 10,
        aspect: 1.7777777777777777,
        filmGauge: 35,
        filmOffset: 0
      }
    },
    project: sceneData.project || {
      shadows: true,
      shadowType: 1,
      toneMapping: 0,
      toneMappingExposure: 1
    },
    scripts: sceneData.scripts || {},
    history: sceneData.history || {
      undos: [],
      redos: []
    },
    environment: sceneData.environment || null
  }

  console.log('处理后的场景数据:', processedData)
  return processedData
}

// 通过URL加载场景JSON文件 - 直接调用编辑器的导入功能
const loadSceneFromUrl = async (sceneJsonUrl) => {
  if (!editor || !sceneJsonUrl) return

  try {
    console.log('正在从URL加载场景:', sceneJsonUrl)
    // 如果还没有设置加载状态，则设置它
    if (!isLoadingScene.value) {
      isLoadingScene.value = true
    }

    // 获取场景JSON文件
    const response = await fetch(sceneJsonUrl)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const sceneText = await response.text()
    console.log('原始场景文本长度:', sceneText.length)

    const importData = JSON.parse(sceneText)
    console.log('解析后的导入数据:', importData)

    // 直接调用编辑器的导入功能
    await triggerEditorImport(sceneText)

    return importData
  } catch (error) {
    console.error('从URL加载场景失败:', error)
    console.error('错误详情:', error.message)
    throw error
  } finally {
    isLoadingScene.value = false
  }
}

// 直接触发编辑器的导入完整场景功能
const triggerEditorImport = async (sceneJsonText) => {
  console.log('直接触发编辑器导入功能...')

  const editorWindow = editorFrame.value?.contentWindow
  if (!editorWindow) {
    throw new Error('无法访问编辑器窗口')
  }

  try {
    // 创建一个虚拟的File对象
    const blob = new Blob([sceneJsonText], { type: 'application/json' })
    const file = new File([blob], 'scene.json', { type: 'application/json' })

    console.log('创建的文件对象:', {
      name: file.name,
      size: file.size,
      type: file.type
    })

    // 查找编辑器中的导入场景输入元素
    // 需要找到正确的输入元素，可能有多个json文件输入
    const allJsonInputs = editorWindow.document.querySelectorAll('input[type="file"][accept=".json"]')
    console.log('找到的JSON文件输入元素数量:', allJsonInputs.length)

    // 通常导入场景的输入元素是第二个（第一个是打开项目）
    const importSceneInput = allJsonInputs.length > 1 ? allJsonInputs[1] : allJsonInputs[0]

    if (importSceneInput) {
      console.log('找到导入场景输入元素，直接设置文件并触发事件')

      // 创建一个FileList对象
      const dataTransfer = new editorWindow.DataTransfer()
      dataTransfer.items.add(file)

      // 设置文件到输入元素
      importSceneInput.files = dataTransfer.files

      // 触发change事件
      const changeEvent = new editorWindow.Event('change', { bubbles: true })
      importSceneInput.dispatchEvent(changeEvent)

      console.log('导入场景事件已触发')
    } else {
      console.warn('未找到导入场景输入元素，尝试直接复制编辑器的导入逻辑')

      // 直接复制Three.js编辑器中"导入完整场景"的处理逻辑
      const importData = JSON.parse(sceneJsonText)
      await executeImportSceneLogic(editorWindow, importData)
    }

  } catch (importError) {
    console.error('触发编辑器导入功能失败:', importError)
    throw importError
  } finally {
    isLoadingScene.value = false
  }
}

// 直接执行Three.js编辑器的导入完整场景逻辑
const executeImportSceneLogic = async (editorWindow, importData) => {
  console.log('执行编辑器的导入完整场景逻辑...')

  const editor = editorWindow.editor
  const THREE = editorWindow.THREE

  if (!editor || !THREE) {
    throw new Error('无法访问编辑器或Three.js对象')
  }

  // 验证JSON格式
  if (!importData || typeof importData !== 'object') {
    throw new Error('JSON文件格式无效')
  }

  console.log('导入的数据结构:', importData)
  console.log('包含的亮点数据:', importData.spotlights)
  console.log('包含的光源数据:', importData.lights)

  // 清空当前场景
  console.log('清空当前场景...')
  editor.clear()

  // 根据导入数据的格式进行处理
  let sceneData
  let importedObjectCount = 0

  if (importData.scene) {
    // 新格式：包含完整导出信息
    console.log('检测到新格式，包含完整场景信息')
    sceneData = importData.scene

    // 恢复相机位置
    if (importData.camera && Array.isArray(importData.camera.position)) {
      console.log('恢复相机位置:', importData.camera.position)
      editor.camera.position.fromArray(importData.camera.position)

      if (Array.isArray(importData.camera.rotation)) {
        const [x, y, z, order] = importData.camera.rotation
        editor.camera.rotation.set(x, y, z, order || 'XYZ')
      }

      if (importData.camera.zoom) {
        editor.camera.zoom = importData.camera.zoom
      }

      if (importData.camera.fov) {
        editor.camera.fov = importData.camera.fov
        editor.camera.updateProjectionMatrix()
      }
    }

    // 恢复编辑器设置
    if (importData.settings) {
      console.log('恢复编辑器设置:', importData.settings)

      if (importData.settings.viewportShading) {
        editor.viewportShading = importData.settings.viewportShading
      }

      if (importData.settings.gridVisible !== undefined && editor.grid) {
        editor.grid.visible = importData.settings.gridVisible
      }

      if (importData.settings.backgroundColor !== undefined) {
        editor.scene.background = new THREE.Color(importData.settings.backgroundColor)
      }
    }

  } else if (importData.object || importData.metadata) {
    // 兼容旧格式：直接是场景数据
    console.log('检测到旧格式场景数据')
    sceneData = importData

  } else {
    throw new Error('无效的场景JSON格式，请确保文件是从Three.js编辑器导出的场景文件')
  }

  // 使用ObjectLoader加载场景数据
  console.log('使用ObjectLoader加载场景数据...')
  const loader = new THREE.ObjectLoader()

  try {
    const loadedScene = await loader.parseAsync(sceneData)
    console.log('场景数据解析成功，对象数量:', loadedScene.children.length)

    // 设置场景
    editor.setScene(loadedScene)
    importedObjectCount = loadedScene.children.length

    console.log('场景设置完成，导入对象数量:', importedObjectCount)

  } catch (parseError) {
    console.error('场景数据解析失败:', parseError)
    throw new Error(`场景数据解析失败: ${parseError.message}`)
  }

  console.log('完整场景导入成功，导入对象数量:', importedObjectCount)
}

// 模拟Three.js编辑器的导入完整场景功能
const importCompleteScene = async (importData) => {
  console.log('开始导入完整场景...')

  // 验证JSON格式
  if (!importData || typeof importData !== 'object') {
    throw new Error('JSON文件格式无效')
  }

  // 清空当前场景
  console.log('清空当前场景...')
  editor.clear()

  // 根据导入数据的格式进行处理
  let sceneData

  if (importData.scene) {
    // 新格式：包含完整导出信息（自定义格式）
    console.log('检测到自定义导出格式，包含完整场景信息')
    sceneData = importData.scene

    // 恢复相机位置
    if (importData.camera && Array.isArray(importData.camera.position)) {
      console.log('恢复相机位置:', importData.camera.position)
      editor.camera.position.fromArray(importData.camera.position)

      if (Array.isArray(importData.camera.rotation)) {
        const [x, y, z, order] = importData.camera.rotation
        editor.camera.rotation.set(x, y, z, order || 'XYZ')
      }

      if (importData.camera.zoom) {
        editor.camera.zoom = importData.camera.zoom
      }

      if (importData.camera.fov) {
        editor.camera.fov = importData.camera.fov
        editor.camera.updateProjectionMatrix()
      }
    }

    // 恢复编辑器设置
    if (importData.settings) {
      console.log('恢复编辑器设置:', importData.settings)

      if (importData.settings.viewportShading) {
        editor.viewportShading = importData.settings.viewportShading
      }

      if (importData.settings.gridVisible !== undefined && editor.grid) {
        editor.grid.visible = importData.settings.gridVisible
      }

      if (importData.settings.backgroundColor !== undefined) {
        // 通过编辑器窗口获取THREE对象
        const editorWindow = editorFrame.value?.contentWindow
        if (editorWindow && editorWindow.THREE) {
          editor.scene.background = new editorWindow.THREE.Color(importData.settings.backgroundColor)
        }
      }
    }

  } else if (importData.project && importData.camera && importData.scene) {
    // Three.js编辑器标准格式
    console.log('检测到Three.js编辑器标准格式')

    // 直接使用editor.fromJSON加载
    await editor.fromJSON(importData)
    console.log('标准格式场景加载完成')
    return

  } else if (importData.object || importData.metadata) {
    // 兼容旧格式：直接是场景数据
    console.log('检测到旧格式场景数据')
    sceneData = importData

  } else {
    throw new Error('无效的场景JSON格式，请确保文件是从Three.js编辑器导出的场景文件')
  }

  // 使用ObjectLoader加载场景数据
  console.log('使用ObjectLoader加载场景数据...')

  // 通过编辑器窗口获取THREE对象
  const editorWindow = editorFrame.value?.contentWindow
  if (!editorWindow || !editorWindow.THREE) {
    throw new Error('无法访问Three.js对象，编辑器可能未完全加载')
  }

  const loader = new editorWindow.THREE.ObjectLoader()

  try {
    const loadedScene = await loader.parseAsync(sceneData)
    console.log('场景数据解析成功:', loadedScene)

    // 设置场景
    editor.setScene(loadedScene)
    console.log('场景设置完成')

    // 触发相关信号
    editor.signals.sceneGraphChanged.dispatch()

  } catch (parseError) {
    console.error('场景数据解析失败:', parseError)
    throw new Error(`场景数据解析失败: ${parseError.message}`)
  }

  console.log('完整场景导入成功')
}

// 深度检查和修复场景数据中的所有对象
const fixSceneObjects = (obj, path = 'root') => {
  if (!obj || typeof obj !== 'object') {
    return obj
  }

  // 如果是数组，递归处理每个元素
  if (Array.isArray(obj)) {
    return obj.map((item, index) => fixSceneObjects(item, `${path}[${index}]`))
  }

  // 创建新对象，避免修改原始数据
  const fixedObj = { ...obj }

  // 检查是否是Three.js对象（通常有uuid字段）
  if (fixedObj.uuid) {
    // 如果缺少type字段，尝试推断或设置默认值
    if (!fixedObj.type) {
      console.warn(`对象 ${path} (uuid: ${fixedObj.uuid}) 缺少 type 字段，尝试修复...`)

      // 根据其他属性推断type
      if (fixedObj.geometry !== undefined && fixedObj.material !== undefined) {
        fixedObj.type = 'Mesh'
      } else if (fixedObj.fov !== undefined) {
        fixedObj.type = 'PerspectiveCamera'
      } else if (fixedObj.left !== undefined && fixedObj.right !== undefined) {
        fixedObj.type = 'OrthographicCamera'
      } else if (fixedObj.color !== undefined && fixedObj.intensity !== undefined) {
        fixedObj.type = 'DirectionalLight'
      } else if (fixedObj.children !== undefined) {
        fixedObj.type = 'Group'
      } else {
        // 默认设置为Object3D
        fixedObj.type = 'Object3D'
      }

      console.log(`对象 ${path} 的 type 已设置为: ${fixedObj.type}`)
    }
  }

  // 递归处理所有属性
  for (const key in fixedObj) {
    if (fixedObj.hasOwnProperty(key) && typeof fixedObj[key] === 'object') {
      fixedObj[key] = fixSceneObjects(fixedObj[key], `${path}.${key}`)
    }
  }

  return fixedObj
}

// 验证场景数据格式
const validateSceneData = (sceneData) => {
  if (!sceneData || typeof sceneData !== 'object') {
    console.error('场景数据不是有效的对象')
    return false
  }

  // 检查是否有必要的字段
  if (!sceneData.metadata) {
    console.error('场景数据缺少 metadata 字段')
    return false
  }

  // 检查是否是Three.js编辑器标准格式
  if (sceneData.project && sceneData.scene && sceneData.camera) {
    console.log('检测到Three.js编辑器标准格式')
    return true
  }

  // 检查是否是自定义导出格式（包含camera.position等字段）
  if (sceneData.camera && Array.isArray(sceneData.camera.position)) {
    console.log('检测到自定义导出格式')
    return true
  }

  // 检查标准Three.js场景格式
  if (!sceneData.scene && !sceneData.object) {
    console.error('场景数据缺少 scene 或 object 字段')
    return false
  }

  console.log('场景数据格式验证通过')
  return true
}

const addObject = (object) => {
  if (!editor) return
  try {
    editor.addObject(object)
  } catch (error) {
    console.error('添加对象失败:', error)
  }
}

const removeObject = (object) => {
  if (!editor) return
  try {
    editor.removeObject(object)
  } catch (error) {
    console.error('移除对象失败:', error)
  }
}

const selectObject = (object) => {
  if (!editor) return
  try {
    editor.select(object)
  } catch (error) {
    console.error('选择对象失败:', error)
  }
}

const clearScene = () => {
  if (!editor) return
  try {
    editor.clear()
  } catch (error) {
    console.error('清空场景失败:', error)
  }
}

const exportScene = (format = 'json') => {
  if (!editor) return null

  try {
    switch (format) {
      case 'json':
        return editor.toJSON()
      case 'gltf':
        console.warn('GLTF导出功能需要在原始编辑器中实现')
        return null
      default:
        return editor.toJSON()
    }
  } catch (error) {
    console.error('导出场景失败:', error)
    return null
  }
}

const importFile = (file) => {
  if (!editor || !file) return
  try {
    if (editor.loader && editor.loader.loadFile) {
      editor.loader.loadFile(file)
    } else {
      console.warn('编辑器不支持文件导入功能')
    }
  } catch (error) {
    console.error('导入文件失败:', error)
  }
}

const executeCommand = (commandName, ...args) => {
  if (!editor) return

  try {
    if (editor[commandName] && typeof editor[commandName] === 'function') {
      return editor[commandName](...args)
    } else {
      console.warn(`编辑器不支持命令: ${commandName}`)
    }
  } catch (error) {
    console.error(`执行命令 ${commandName} 失败:`, error)
  }
}

const getEditorState = () => {
  if (!editor) return null

  try {
    return {
      scene: editor.scene,
      camera: editor.camera,
      renderer: editor.renderer,
      selected: editor.selected,
      isReady: isReady.value
    }
  } catch (error) {
    console.error('获取编辑器状态失败:', error)
    return null
  }
}

// 监听props变化
watch(() => props.visible, (visible) => {
  if (visible && !isReady.value) {
    // iframe会自动加载，无需手动初始化
  }
})

watch(() => props.initialScene, (newScene) => {
  if (newScene && isReady.value) {
    loadScene(newScene)
  }
})

// 监听模型库数据变化
watch(() => props.modelLibraryData, (newData) => {
  if (newData && isReady.value && editorFrame.value) {
    const iframe = editorFrame.value
    if (iframe.contentWindow && iframe.contentWindow.setModelLibraryData) {
      iframe.contentWindow.setModelLibraryData(newData)
      console.log('模型库数据已更新到编辑器:', newData)
    }
  }
}, { deep: true })

// 监听亮点数据变化
watch(() => props.spotlightData, (newData) => {
  console.log('ThreeEditor组件收到亮点数据:', newData)
  if (newData && newData.length > 0) {
    // 如果编辑器还没准备好，等待一下再传递
    if (!isReady.value) {
      console.log('编辑器还没准备好，等待传递亮点数据...')
      const checkReady = () => {
        if (isReady.value && editorFrame.value) {
          const iframe = editorFrame.value
          if (iframe.contentWindow && iframe.contentWindow.setSpotlightData) {
            iframe.contentWindow.setSpotlightData(newData)
            console.log('亮点数据已延迟更新到编辑器iframe:', newData)
          }
        } else {
          setTimeout(checkReady, 100) // 100ms后再检查
        }
      }
      setTimeout(checkReady, 100)
    } else {
      // 编辑器已准备好，直接传递
      const iframe = editorFrame.value
      if (iframe && iframe.contentWindow && iframe.contentWindow.setSpotlightData) {
        iframe.contentWindow.setSpotlightData(newData)
        console.log('亮点数据已更新到编辑器iframe:', newData)
      } else {
        console.log('iframe.contentWindow.setSpotlightData 不存在')
      }
    }
  } else {
    console.log('亮点数据传递条件不满足:', { newData: !!newData, isReady: isReady.value, editorFrame: !!editorFrame.value })
  }
}, { deep: true })

onMounted(() => {
  // iframe会自动加载编辑器
  console.log('ThreeEditor组件已挂载')
})

onUnmounted(() => {
  // 清理工作
  editor = null
  console.log('ThreeEditor组件已卸载')
})

// 暴露方法给父组件
defineExpose({
  getSceneData,
  loadScene,
  loadSceneFromUrl,
  addObject,
  removeObject,
  selectObject,
  clearScene,
  exportScene,
  importFile,
  executeCommand,
  getEditorState,
  isReady: () => isReady.value,
  getEditor: () => editor,
  setLoadingScene: (loading) => { isLoadingScene.value = loading }
})
</script>

<style scoped>
.three-editor-container {
  position: relative;
  width: 100%;
  height: 100%;
  font-family: 'Lucida Grande', sans-serif;
  font-size: 12px;
  background: #ddd;
}

.editor-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: #ddd;
}

.editor-iframe {
  width: 100%;
  height: 100%;
  border: none;
  display: block;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  text-align: center;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>