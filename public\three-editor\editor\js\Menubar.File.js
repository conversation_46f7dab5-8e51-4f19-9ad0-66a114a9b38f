import { UIPanel, UIRow, UIHorizontalRule } from './libs/ui.js';

function <PERSON>ubar<PERSON>ile( editor ) {

	const strings = editor.strings;

	const saveArrayBuffer = editor.utils.saveArrayBuffer;
	const saveString = editor.utils.saveString;

	const container = new UIPanel();
	container.setClass( 'menu' );

	const title = new UIPanel();
	title.setClass( 'title' );
	title.setTextContent( strings.getKey( 'menubar/file' ) );
	container.add( title );

	const options = new UIPanel();
	options.setClass( 'options' );
	container.add( options );

	// New Project

	const newProjectSubmenuTitle = new UIRow().setTextContent( strings.getKey( 'menubar/file/new' ) ).addClass( 'option' ).addClass( 'submenu-title' );
	newProjectSubmenuTitle.onMouseOver( function () {

		const { top, right } = this.dom.getBoundingClientRect();
		const { paddingTop } = getComputedStyle( this.dom );
		newProjectSubmenu.setLeft( right + 'px' );
		newProjectSubmenu.setTop( top - parseFloat( paddingTop ) + 'px' );
		newProjectSubmenu.setDisplay( 'block' );

	} );
	newProjectSubmenuTitle.onMouseOut( function () {

		newProjectSubmenu.setDisplay( 'none' );

	} );
	options.add( newProjectSubmenuTitle );

	const newProjectSubmenu = new UIPanel().setPosition( 'fixed' ).addClass( 'options' ).setDisplay( 'none' );
	newProjectSubmenuTitle.add( newProjectSubmenu );

	// New Project / Empty

	let option = new UIRow().setTextContent( strings.getKey( 'menubar/file/new/empty' ) ).setClass( 'option' );
	option.onClick( function () {

		if ( confirm( strings.getKey( 'prompt/file/open' ) ) ) {

			editor.clear();

		}

	} );
	newProjectSubmenu.add( option );

	//

	newProjectSubmenu.add( new UIHorizontalRule() );

	// New Project / ...

	const examples = [
		{ title: 'menubar/file/new/Arkanoid', file: 'arkanoid.app.json' },
		{ title: 'menubar/file/new/Camera', file: 'camera.app.json' },
		{ title: 'menubar/file/new/Particles', file: 'particles.app.json' },
		{ title: 'menubar/file/new/Pong', file: 'pong.app.json' },
		{ title: 'menubar/file/new/Shaders', file: 'shaders.app.json' }
	];

	const loader = new THREE.FileLoader();

	for ( let i = 0; i < examples.length; i ++ ) {

		( function ( i ) {

			const example = examples[ i ];

			const option = new UIRow();
			option.setClass( 'option' );
			option.setTextContent( strings.getKey( example.title ) );
			option.onClick( function () {

				if ( confirm( strings.getKey( 'prompt/file/open' ) ) ) {

					loader.load( 'examples/' + example.file, function ( text ) {

						editor.clear();
						editor.fromJSON( JSON.parse( text ) );

					} );

				}

			} );
			newProjectSubmenu.add( option );

		} )( i );

	}

	// Open

	const openProjectForm = document.createElement( 'form' );
	openProjectForm.style.display = 'none';
	document.body.appendChild( openProjectForm );

	const openProjectInput = document.createElement( 'input' );
	openProjectInput.multiple = false;
	openProjectInput.type = 'file';
	openProjectInput.accept = '.json';
	openProjectInput.addEventListener( 'change', async function () {

		const file = openProjectInput.files[ 0 ];

		if ( file === undefined ) return;

		try {

			const json = JSON.parse( await file.text() );

			async function onEditorCleared() {

				await editor.fromJSON( json );

				editor.signals.editorCleared.remove( onEditorCleared );

			}

			editor.signals.editorCleared.add( onEditorCleared );

			editor.clear();

		} catch ( e ) {

			alert( strings.getKey( 'prompt/file/failedToOpenProject' ) );
			console.error( e );

		} finally {

			form.reset();

		}

	} );

	openProjectForm.appendChild( openProjectInput );

	option = new UIRow()
		.addClass( 'option' )
		.setTextContent( strings.getKey( 'menubar/file/open' ) )
		.onClick( function () {

			if ( confirm( strings.getKey( 'prompt/file/open' ) ) ) {

				openProjectInput.click();

			}

		} );

	options.add( option );

	// Save

	option = new UIRow()
		.addClass( 'option' )
		.setTextContent( strings.getKey( 'menubar/file/save' ) )
		.onClick( function () {

			const json = editor.toJSON();
			const blob = new Blob( [ JSON.stringify( json ) ], { type: 'application/json' } );
			editor.utils.save( blob, 'project.json' );

		} );

	options.add( option );

	//

	options.add( new UIHorizontalRule() );

	// Import

	const form = document.createElement( 'form' );
	form.style.display = 'none';
	document.body.appendChild( form );

	const fileInput = document.createElement( 'input' );
	fileInput.multiple = true;
	fileInput.type = 'file';
	fileInput.addEventListener( 'change', function () {

		editor.loader.loadFiles( fileInput.files );
		form.reset();

	} );
	form.appendChild( fileInput );

	option = new UIRow();
	option.setClass( 'option' );
	option.setTextContent( strings.getKey( 'menubar/file/import' ) );
	option.onClick( function () {

		fileInput.click();

	} );
	options.add( option );

	// Import Scene JSON (一键导入场景)

	const importSceneForm = document.createElement( 'form' );
	importSceneForm.style.display = 'none';
	document.body.appendChild( importSceneForm );

	const importSceneInput = document.createElement( 'input' );
	importSceneInput.multiple = false;
	importSceneInput.type = 'file';
	importSceneInput.accept = '.json';
	importSceneInput.addEventListener( 'change', async function () {

		const file = importSceneInput.files[ 0 ];

		if ( file === undefined ) return;

		try {

			// 检查文件大小（限制为200MB）
			const maxSize = 200 * 1024 * 1024; // 200MB
			if ( file.size > maxSize ) {
				alert( `文件过大！文件大小: ${Math.round(file.size / 1024 / 1024)}MB，最大支持: 200MB` );
				return;
			}

			// 检查文件扩展名
			if ( !file.name.toLowerCase().endsWith('.json') ) {
				alert( '请选择JSON格式的场景文件！' );
				return;
			}

			console.log(`正在导入场景文件: ${file.name} (${Math.round(file.size / 1024)}KB)`);

			const text = await file.text();
			const importData = JSON.parse( text );

			// 调试信息
			console.log('导入的数据结构:', importData);
			console.log('包含的亮点数据:', importData.spotlights);
			console.log('包含的光源数据:', importData.lights);
			console.log('数据类型检查:', {
				hasSpotlights: !!importData.spotlights,
				spotlightsIsArray: Array.isArray(importData.spotlights),
				spotlightsLength: importData.spotlights ? importData.spotlights.length : 0,
				hasLights: !!importData.lights,
				lightsIsArray: Array.isArray(importData.lights),
				lightsLength: importData.lights ? importData.lights.length : 0
			});

			// 验证JSON格式
			if ( !importData || typeof importData !== 'object' ) {
				throw new Error( 'JSON文件格式无效' );
			}

			// 显示场景信息
			let sceneInfo = `文件: ${file.name}\n`;
			if ( importData.metadata ) {
				if ( importData.metadata.exportTime ) {
					sceneInfo += `导出时间: ${new Date(importData.metadata.exportTime).toLocaleString()}\n`;
				}
				if ( importData.metadata.objectCount !== undefined ) {
					sceneInfo += `对象数量: ${importData.metadata.objectCount}\n`;
				}
				if ( importData.metadata.meshCount !== undefined ) {
					sceneInfo += `网格数量: ${importData.metadata.meshCount}\n`;
				}
				if ( importData.metadata.lightCount !== undefined ) {
					sceneInfo += `光源数量: ${importData.metadata.lightCount}\n`;
				}
				if ( importData.metadata.spotlightCount !== undefined ) {
					sceneInfo += `亮点数量: ${importData.metadata.spotlightCount}\n`;
				}
			}

			// 检查实际数据
			const actualSpotlightCount = importData.spotlights ? importData.spotlights.length : 0;
			const actualLightCount = importData.lights ? importData.lights.length : 0;
			if ( actualSpotlightCount > 0 || actualLightCount > 0 ) {
				sceneInfo += `\n将恢复:\n`;
				if ( actualLightCount > 0 ) {
					sceneInfo += `- ${actualLightCount}个光源的详细设置\n`;
				}
				if ( actualSpotlightCount > 0 ) {
					sceneInfo += `- ${actualSpotlightCount}个亮点数据\n`;
				}
			}

			sceneInfo += `\n导入场景将替换当前场景，是否继续？`;

			// 确认是否要替换当前场景
			if ( confirm( sceneInfo ) ) {

				console.log('开始导入场景...');

				// 清空当前场景
				editor.clear();

				// 根据导入数据的格式进行处理
				let sceneData;
				let importedObjectCount = 0;

				if ( importData.scene ) {
					// 新格式：包含完整导出信息
					sceneData = importData.scene;

					// 恢复相机设置（延迟执行确保相机已初始化）
					if ( importData.camera && editor.camera ) {
						setTimeout( () => {
							try {
								const camera = editor.camera;
								if ( camera && camera.isCamera ) {
									console.log('导入前相机位置:', camera.position.toArray());
									console.log('导入前相机旋转:', camera.rotation.toArray());

									if ( importData.camera.position ) {
										camera.position.fromArray( importData.camera.position );
										console.log('相机位置已恢复:', camera.position.toArray());
									}
									if ( importData.camera.rotation ) {
										camera.rotation.fromArray( importData.camera.rotation );
										console.log('相机旋转已恢复:', camera.rotation.toArray());
									}
									if ( importData.camera.zoom ) {
										camera.zoom = importData.camera.zoom;
									}
									if ( importData.camera.fov && camera.isPerspectiveCamera ) {
										camera.fov = importData.camera.fov;
									}
									if ( importData.camera.near ) {
										camera.near = importData.camera.near;
									}
									if ( importData.camera.far ) {
										camera.far = importData.camera.far;
									}
									camera.updateProjectionMatrix();
									console.log('相机设置已恢复');

									// 触发相机更新信号
									if ( editor.signals && editor.signals.cameraChanged ) {
										editor.signals.cameraChanged.dispatch( camera );
									}
								} else {
									console.warn('相机对象无效，跳过相机设置恢复');
								}
							} catch ( error ) {
								console.error('恢复相机设置失败:', error);
							}
						}, 200 );
					}

					// 恢复编辑器设置
					if ( importData.settings ) {
						if ( importData.settings.viewportShading ) {
							editor.viewportShading = importData.settings.viewportShading;
						}
						if ( importData.settings.gridVisible !== undefined && editor.grid ) {
							editor.grid.visible = importData.settings.gridVisible;
						}
						if ( importData.settings.backgroundColor !== undefined ) {
							editor.scene.background = new THREE.Color( importData.settings.backgroundColor );
						}
						console.log('编辑器设置已恢复');
					}

				} else if ( importData.object || importData.metadata ) {
					// 兼容旧格式：直接是场景数据
					sceneData = importData;
				} else {
					throw new Error( '无效的场景JSON格式，请确保文件是从Three.js编辑器导出的场景文件' );
				}

				// 使用ObjectLoader加载场景
				const loader = new THREE.ObjectLoader();
				console.log('🔍 开始解析场景数据，包含的键:', Object.keys(sceneData));

				// 检查场景数据结构
				if ( sceneData.geometries ) {
					console.log(`📐 场景包含 ${sceneData.geometries.length} 个几何体定义`);
				}
				if ( sceneData.materials ) {
					console.log(`🎨 场景包含 ${sceneData.materials.length} 个材质定义`);
				}
				if ( sceneData.textures ) {
					console.log(`🖼️ 场景包含 ${sceneData.textures.length} 个纹理定义`);
				}
				if ( sceneData.images ) {
					console.log(`📷 场景包含 ${sceneData.images.length} 个图片定义`);
				}

				// 分析原始场景数据中的对象结构
				if ( sceneData.object && sceneData.object.children ) {
					console.log(`🔍 原始场景数据分析:`);
					console.log(`  - 原始场景包含 ${sceneData.object.children.length} 个顶级对象`);
					sceneData.object.children.forEach((child, index) => {
						console.log(`    ${index + 1}. ${child.name || '未命名'} (${child.type})`);
						if (child.children && child.children.length > 0) {
							console.log(`       └─ 包含 ${child.children.length} 个子对象`);
						}
					});
				}

				const loadedScene = loader.parse( sceneData );
				console.log('✅ 场景数据解析成功:', loadedScene);

				// 详细分析场景结构
				console.log('🔍 场景详细分析:');
				console.log('  - 场景UUID:', loadedScene.uuid);
				console.log('  - 场景名称:', loadedScene.name);
				console.log('  - 顶级子对象数量:', loadedScene.children.length);

				// 遍历所有顶级对象
				loadedScene.children.forEach((child, index) => {
					console.log(`  ${index + 1}. ${child.name || '未命名'} (${child.type})`);
					if (child.children && child.children.length > 0) {
						console.log(`     └─ 包含 ${child.children.length} 个子对象`);
						child.children.forEach((subChild, subIndex) => {
							console.log(`        ${subIndex + 1}. ${subChild.name || '未命名'} (${subChild.type})`);
						});
					}
				});

				// 统计所有对象类型
				const objectTypes = {};
				loadedScene.traverse((obj) => {
					objectTypes[obj.type] = (objectTypes[obj.type] || 0) + 1;
				});
				console.log('📊 对象类型统计:', objectTypes);

				// 将加载的对象添加到编辑器场景中
				if ( loadedScene.children ) {
					console.log(`📦 准备添加 ${loadedScene.children.length} 个顶级对象到场景中`);

					// 比较原始数据和解析后的数据
					if ( sceneData.object && sceneData.object.children ) {
						console.log(`🔍 数据对比:`);
						console.log(`  - 原始场景对象数量: ${sceneData.object.children.length}`);
						console.log(`  - 解析后对象数量: ${loadedScene.children.length}`);

						if ( sceneData.object.children.length !== loadedScene.children.length ) {
							console.warn(`⚠️ 对象数量不匹配！可能有对象在解析过程中丢失`);

							// 详细比较每个对象
							sceneData.object.children.forEach( (originalChild, index) => {
								const parsedChild = loadedScene.children[index];
								if ( parsedChild ) {
									console.log(`  ${index + 1}. ✅ ${originalChild.name} (${originalChild.type}) -> ${parsedChild.name} (${parsedChild.type})`);
								} else {
									console.log(`  ${index + 1}. ❌ ${originalChild.name} (${originalChild.type}) -> 缺失`);
								}
							} );
						}
					}

					// 递归函数来添加所有对象（包括嵌套的对象）
					function addObjectRecursively( object, level = 0 ) {
						const indent = '  '.repeat( level );
						console.log(`${indent}- 开始添加对象: ${object.name || '未命名'} (${object.type})`);

						if ( object.isMesh ) {
							console.log(`${indent}  几何体: ${object.geometry ? object.geometry.type : '无'}`);
							console.log(`${indent}  材质: ${object.material ? object.material.type : '无'}`);
						} else if ( object.isGroup ) {
							console.log(`${indent}  组对象，包含 ${object.children.length} 个子对象`);
						} else if ( object.isLight ) {
							console.log(`${indent}  光源类型: ${object.type}`);
						} else if ( object.isSprite ) {
							console.log(`${indent}  精灵对象`);
						}

						// 添加到编辑器
						if ( level === 0 ) {
							console.log(`${indent}  准备添加顶级对象到编辑器...`);
							try {
								editor.addObject( object );
								importedObjectCount++;
								console.log(`${indent}  ✅ 顶级对象添加成功，当前计数: ${importedObjectCount}`);
							} catch (error) {
								console.error(`${indent}  ❌ 添加顶级对象失败:`, error);
								throw error;
							}
						}

						// 递归处理子对象
						if ( object.children && object.children.length > 0 ) {
							console.log(`${indent}  开始处理 ${object.children.length} 个子对象:`);
							try {
								object.children.forEach( (child, childIndex) => {
									console.log(`${indent}    处理子对象 ${childIndex + 1}/${object.children.length}: ${child.name || '未命名'}`);
									addObjectRecursively( child, level + 1 );
								} );
								console.log(`${indent}  ✅ 所有子对象处理完成`);
							} catch (error) {
								console.error(`${indent}  ❌ 处理子对象时出错:`, error);
								throw error;
							}
						}

						console.log(`${indent}- 对象添加完成: ${object.name || '未命名'} (${object.type})`);
					}

					// 详细记录每个要添加的对象
					console.log('🔄 开始逐个添加对象:');
					console.log(`📊 loadedScene.children 实际长度: ${loadedScene.children.length}`);
					console.log(`📊 loadedScene.children 内容:`, loadedScene.children);

					// 详细列出每个对象
					loadedScene.children.forEach((child, index) => {
						console.log(`  ${index}: ${child.name} (${child.type}) - UUID: ${child.uuid}`);
					});

					// 强制处理所有对象，包括可能被过滤的对象
					// 重要：复制对象数组，避免在处理过程中数组被修改导致循环提前结束
					const objectsToProcess = [...loadedScene.children];
					console.log(`🔄 开始for循环，总共要处理 ${objectsToProcess.length} 个对象`);
					console.log(`📋 要处理的对象列表:`, objectsToProcess.map(obj => `${obj.name}(${obj.type})`));

					for ( let index = 0; index < objectsToProcess.length; index++ ) {
						console.log(`\n🔄 === 开始处理第 ${index + 1}/${objectsToProcess.length} 个对象 ===`);
						const child = objectsToProcess[index];
						console.log(`📌 对象信息: ${child.name || '未命名'} (${child.type})`);
						console.log(`   - UUID: ${child.uuid}`);
						console.log(`   - 是否为Group: ${child.isGroup}`);
						console.log(`   - 子对象数量: ${child.children ? child.children.length : 0}`);

						// 特别检查Group对象
						if (child.isGroup) {
							console.log(`🎯 发现Group对象: ${child.name}`);
							console.log(`   - Group子对象:`, child.children.map(c => `${c.name}(${c.type})`));
							console.log(`   - Group可见性: ${child.visible}`);
							console.log(`   - Group位置:`, child.position.toArray());
							console.log(`   - Group缩放:`, child.scale.toArray());
							console.log(`   - Group旋转:`, child.rotation.toArray());

							// 检查前几个子对象的材质和几何体
							const maxCheck = Math.min(5, child.children.length);
							console.log(`   - 检查前${maxCheck}个子对象:`);
							for (let i = 0; i < maxCheck; i++) {
								const subChild = child.children[i];
								if (subChild.isMesh) {
									console.log(`     子对象${i}: ${subChild.name}`);
									console.log(`       - 可见性: ${subChild.visible}`);
									console.log(`       - 几何体: ${subChild.geometry ? subChild.geometry.type : '无'}`);
									console.log(`       - 材质: ${subChild.material ? subChild.material.type : '无'}`);
									if (subChild.material) {
										console.log(`       - 材质可见: ${subChild.material.visible}`);
										console.log(`       - 材质透明度: ${subChild.material.opacity}`);
									}
								}
							}
						}

						console.log(`🔄 准备调用addObjectRecursively...`);
						try {
							addObjectRecursively( child );
							console.log(`✅ 第 ${index + 1} 个对象添加成功`);
						} catch (error) {
							console.error(`❌ 第 ${index + 1} 个对象添加失败:`, error);
							console.error(`   错误详情:`, error.stack);
							console.error(`   继续处理下一个对象...`);
						}
						console.log(`🔄 === 第 ${index + 1} 个对象处理完成 ===\n`);
					}
					console.log(`🔄 对象添加过程完成，实际处理了 ${objectsToProcess.length} 个对象`);
					console.log(`📊 当前场景中的顶级对象数量: ${editor.scene.children.length}`);

					console.log(`✅ 已添加 ${importedObjectCount} 个顶级对象到场景中`);

					// 统计所有对象（包括嵌套的）
					let totalObjects = 0;
					loadedScene.traverse( () => totalObjects++ );
					console.log(`📊 场景总共包含 ${totalObjects} 个对象（包括嵌套对象）`);

				} else {
					console.warn('⚠️ 场景中没有子对象');
				}

				// 恢复亮点数据（仅用于场景中的精灵对象，不影响左侧看板数据）
				if ( importData.spotlights && Array.isArray( importData.spotlights ) ) {
					// 保存导入的亮点数据到编辑器内部，但不覆盖外部数据源
					if ( !editor.importedSpotlightData ) {
						editor.importedSpotlightData = [];
					}
					editor.importedSpotlightData = importData.spotlights;

					console.log(`已恢复 ${importData.spotlights.length} 个亮点数据到场景中`);
					console.log('💡 注意：左侧看板的亮点数据保持不变，继续使用API请求的数据');

					// 不调用setSpotlightData等方法，避免覆盖左侧看板的数据
					// 这样左侧看板继续显示从API请求的亮点数据
					// 而场景中的精灵对象使用导入的亮点数据

					// 触发场景内部的亮点数据更新事件（如果需要）
					if ( editor.signals && editor.signals.importedSpotlightDataChanged ) {
						editor.signals.importedSpotlightDataChanged.dispatch( importData.spotlights );
					}

					// 检查场景中是否已经有亮点精灵对象
					let existingSprites = 0;
					const existingSpriteData = [];
					editor.scene.traverse( function ( object ) {
						if ( object.isSprite && object.userData && object.userData.type === 'spotlight' ) {
							existingSprites++;
							existingSpriteData.push( object.userData.spotlightData );
						}
					} );

					console.log(`场景中已存在 ${existingSprites} 个亮点精灵对象`);

					// 检查是否需要创建亮点精灵（如果精灵数量不足，需要补充创建）
					const needToCreate = importData.spotlights.length - existingSprites;
					if ( needToCreate > 0 ) {
						console.log(`场景中亮点精灵不足，需要创建 ${needToCreate} 个（总共需要 ${importData.spotlights.length} 个）`);

						// 创建缺失的亮点精灵对象
						const spotlightsToCreate = importData.spotlights.slice( existingSprites );
						spotlightsToCreate.forEach( spotlight => {
						try {
							console.log( '🎯 重新创建亮点精灵:', spotlight );

							// 检查SVG符号是否存在的函数（参考左侧看板逻辑）
							function checkSvgSymbolExists( iconCode ) {
								const svgElement = document.querySelector( `#icon-${iconCode}` );
								return svgElement !== null;
							}

							// 创建SVG图标的函数（参考左侧看板逻辑）
							function createSvgIcon( iconCode, size = 64, color = 'white' ) {
								const svgIcon = document.createElementNS( 'http://www.w3.org/2000/svg', 'svg' );
								svgIcon.setAttribute( 'viewBox', '0 0 1024 1024' );
								svgIcon.setAttribute( 'width', size );
								svgIcon.setAttribute( 'height', size );
								svgIcon.style.fill = color;

								const useElement = document.createElementNS( 'http://www.w3.org/2000/svg', 'use' );
								useElement.setAttributeNS( 'http://www.w3.org/1999/xlink', 'xlink:href', `#icon-${iconCode}` );
								useElement.setAttribute( 'href', `#icon-${iconCode}` );

								svgIcon.appendChild( useElement );
								return svgIcon;
							}

							// 创建备用图标的函数
							function createFallbackIcon( iconCode, size = 64, color = 'white' ) {
								const iconMap = {
									'education': '🎓',
									'example': '📋',
									'button': '🔘',
									'star': '⭐',
									'monitor': '📺',
									'warning': '⚠️',
									'info': 'ℹ️',
									'settings': '⚙️'
								};

								const iconText = iconMap[iconCode] || iconCode.charAt(0).toUpperCase() || '●';

								const textElement = document.createElement( 'div' );
								textElement.style.cssText = `
									font-size: ${size * 0.8}px;
									color: ${color};
									text-align: center;
									line-height: ${size}px;
									font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", Arial, sans-serif;
								`;
								textElement.textContent = iconText;
								return textElement;
							}

							// 创建亮点精灵的函数（参考ResourcePanel.js的逻辑）
							function createSpotlightTexture( spotlight ) {
								const canvas = document.createElement( 'canvas' );
								const size = 256; // 纹理尺寸
								canvas.width = size;
								canvas.height = size;
								const ctx = canvas.getContext( '2d' );

								// 启用抗锯齿，提高绘制质量
								ctx.imageSmoothingEnabled = true;
								ctx.imageSmoothingQuality = 'high';

								// 绘制圆角矩形背景 - 参考ResourcePanel.js，占满整个canvas
								const rectWidth = size;
								const rectHeight = size;
								const rectX = 0;
								const rectY = 0;
								const cornerRadius = 16; // 适中的圆角半径

								const centerX = size / 2;
								const centerY = size / 2;

								// 解析颜色（支持rgba格式）
								let bgColor = spotlight.iconColor || '#3498db';

								// 处理不同颜色格式
								if ( bgColor.startsWith('rgba') ) {
									ctx.fillStyle = bgColor;
								} else if ( bgColor.startsWith('rgb') ) {
									ctx.fillStyle = bgColor;
								} else if ( bgColor.startsWith('#') ) {
									ctx.fillStyle = bgColor;
								} else {
									// 默认蓝色
									ctx.fillStyle = '#3498db';
								}

								// 绘制背景圆角矩形
								ctx.beginPath();
								ctx.roundRect( rectX, rectY, rectWidth, rectHeight, cornerRadius );
								ctx.fill();

								// 添加边框效果 - 参考ResourcePanel.js
								ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
								ctx.lineWidth = 2;
								ctx.stroke();

								// 绘制图标 - 完全参考ResourcePanel.js的智能缩放逻辑
								if ( spotlight.iconCode ) {
									console.log( `🎨 尝试绘制亮点图标: ${spotlight.iconCode}` );

									// 查找SVG符号
									const symbol = document.querySelector( `#icon-${spotlight.iconCode}` );
									if ( symbol ) {
										console.log( `✅ 找到SVG符号: ${spotlight.iconCode}` );

										// 尝试使用Path2D绘制SVG路径
										try {
											const pathElements = symbol.querySelectorAll( 'path' );
											if ( pathElements.length > 0 ) {
												console.log( `📐 找到 ${pathElements.length} 个path元素` );

												ctx.fillStyle = 'white';
												ctx.save();

												// 计算所有路径的实际边界框 - 完全参考ResourcePanel.js
												let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
												const paths = [];

												pathElements.forEach( pathElement => {
													const pathData = pathElement.getAttribute( 'd' );
													if ( pathData ) {
														const path2D = new Path2D( pathData );
														paths.push( path2D );

														// 创建临时canvas来测量路径边界
														const tempCanvas = document.createElement('canvas');
														tempCanvas.width = 1024;
														tempCanvas.height = 1024;
														const tempCtx = tempCanvas.getContext('2d');
														tempCtx.fillStyle = 'black';
														tempCtx.fill(path2D);

														// 获取ImageData来计算实际边界
														const imageData = tempCtx.getImageData(0, 0, 1024, 1024);
														const data = imageData.data;

														for (let y = 0; y < 1024; y++) {
															for (let x = 0; x < 1024; x++) {
																const index = (y * 1024 + x) * 4;
																if (data[index + 3] > 0) { // alpha > 0，说明有内容
																	minX = Math.min(minX, x);
																	minY = Math.min(minY, y);
																	maxX = Math.max(maxX, x);
																	maxY = Math.max(maxY, y);
																}
															}
														}
													}
												});

												// 如果找到了有效边界
												if (minX !== Infinity && minY !== Infinity && maxX !== -Infinity && maxY !== -Infinity) {
													const pathWidth = maxX - minX;
													const pathHeight = maxY - minY;
													const pathCenterX = minX + pathWidth / 2;
													const pathCenterY = minY + pathHeight / 2;

													// 计算缩放比例，让实际图标内容占满canvas的大部分
													const iconPadding = size * 0.1; // 10%边距
													const availableSize = size - iconPadding * 2;
													const scaleX = availableSize / pathWidth;
													const scaleY = availableSize / pathHeight;
													const scale = Math.min(scaleX, scaleY); // 保持宽高比

													// 移动到canvas中心
													ctx.translate(size / 2, size / 2);
													// 缩放
													ctx.scale(scale, scale);
													// 移动到路径的中心点
													ctx.translate(-pathCenterX, -pathCenterY);

													console.log( `🎨 智能缩放: pathBounds=[${minX},${minY},${maxX},${maxY}], pathSize=[${pathWidth},${pathHeight}], scale=${scale}` );
												} else {
													// 回退到原来的方法
													const iconPadding = size * 0.05;
													const iconSize = size - iconPadding * 2;
													const scale = iconSize / 1024;
													ctx.translate(size / 2, size / 2);
													ctx.scale(scale, scale);
													ctx.translate(-512, -512);
													console.log( `🎨 回退缩放: scale=${scale}` );
												}

												// 绘制所有路径
												paths.forEach( path2D => {
													ctx.fill( path2D );
												});

												ctx.restore();
												console.log( `✅ 使用智能缩放绘制图标: ${spotlight.iconCode}` );
											} else {
												throw new Error( '没有找到path元素' );
											}
										} catch ( error ) {
											console.warn( `⚠️ SVG路径绘制失败: ${error.message}，使用文字备用` );
											// 备用方案：绘制大文字 - 参考ResourcePanel.js
											ctx.fillStyle = 'white';
											const fontSize = Math.floor(size * 0.8); // 字体大小为canvas的80%，更大更清晰
											ctx.font = `bold ${fontSize}px Arial`;
											ctx.textAlign = 'center';
											ctx.textBaseline = 'middle';
											const text = spotlight.iconCode.charAt(0).toUpperCase();
											ctx.fillText( text, centerX, centerY );
											console.log( `📝 绘制备用文字: ${text}, fontSize=${fontSize}` );
										}
									} else {
										console.warn( `⚠️ 未找到SVG符号: ${spotlight.iconCode}，使用文字备用` );
										// 如果没有找到SVG符号，绘制大文字 - 参考ResourcePanel.js
										ctx.fillStyle = 'white';
										const fontSize = Math.floor(size * 0.8); // 字体大小为canvas的80%，更大更清晰
										ctx.font = `bold ${fontSize}px Arial`;
										ctx.textAlign = 'center';
										ctx.textBaseline = 'middle';
										const text = spotlight.iconCode.charAt(0).toUpperCase();
										ctx.fillText( text, centerX, centerY );
									}
								} else {
									// 没有图标代码时绘制默认符号 - 参考ResourcePanel.js
									console.log( '📝 亮点没有iconCode，使用默认图标' );
									const fontSize = Math.floor(size * 0.8); // 字体大小为canvas的80%，更大更清晰
									ctx.font = `bold ${fontSize}px Arial`;
									ctx.textAlign = 'center';
									ctx.textBaseline = 'middle';
									ctx.fillText( '●', centerX, centerY );
								}

								// 添加高光效果
								const gradient = ctx.createRadialGradient( centerX, centerY - 30, 0, centerX, centerY, rectWidth / 2 );
								gradient.addColorStop( 0, 'rgba(255, 255, 255, 0.4)' );
								gradient.addColorStop( 1, 'rgba(255, 255, 255, 0)' );

								ctx.fillStyle = gradient;
								ctx.beginPath();
								ctx.roundRect( rectX, rectY, rectWidth, rectHeight, cornerRadius );
								ctx.fill();

								console.log(`🎨 已创建亮点纹理: ${spotlight.name || '未命名'}, 图标: ${spotlight.iconCode}, 颜色: ${bgColor}`);
								return canvas;
							}

							// 创建Canvas纹理 - 参考ResourcePanel.js
							const canvas = createSpotlightTexture( spotlight );
							const texture = new THREE.CanvasTexture( canvas );
							texture.needsUpdate = true;
							canvas._texture = texture; // 保存引用以便后续更新

							// 创建精灵材质 - 参考ResourcePanel.js
							const material = new THREE.SpriteMaterial( {
								map: texture,
								transparent: true,
								alphaTest: 0.1
							} );

							// 创建精灵 - 参考ResourcePanel.js的尺寸设置
							const sprite = new THREE.Sprite( material );
							sprite.scale.set( 1.2, 1.2, 1 ); // 适中的精灵大小，既清晰可见又不会太大

							// 正确解析亮点名称
							let spriteName = '亮点';
							if ( spotlight.nameI18n && spotlight.nameI18n.zhContent ) {
								spriteName = spotlight.nameI18n.zhContent;
							} else if ( spotlight.name ) {
								spriteName = spotlight.name;
							}
							sprite.name = spriteName;

							console.log(`🏷️ 精灵名称设置: ${spriteName} (原始数据: name="${spotlight.name}", nameI18n=${JSON.stringify(spotlight.nameI18n)})`);
							console.log(`🎨 使用图标: ${spotlight.iconCode}, 颜色: ${spotlight.iconColor}`);
							sprite.userData = {
								type: 'spotlight',
								spotlightData: spotlight
							};

							// 设置位置（如果有保存的位置信息就使用，否则随机）
							if ( spotlight.position ) {
								sprite.position.fromArray( spotlight.position );
							} else {
								sprite.position.set(
									Math.random() * 10 - 5,
									Math.random() * 10 - 5,
									Math.random() * 10 - 5
								);
							}

							// 设置缩放（如果有保存的缩放信息就使用）
							if ( spotlight.scale ) {
								sprite.scale.fromArray( spotlight.scale );
							}

							editor.addObject( sprite );
							console.log( '✅ 亮点精灵已重新创建:', spotlight.name );

						} catch ( error ) {
							console.error( '❌ 重新创建亮点精灵失败:', error );
						}
					} );
					} else {
						console.log('场景中已有足够的亮点精灵，无需重新创建');
					}

					// 更新现有精灵的数据
					if ( existingSprites > 0 ) {
						console.log('更新现有亮点精灵的数据...');
						let spriteIndex = 0;
						editor.scene.traverse( function ( object ) {
							if ( object.isSprite && object.userData && object.userData.type === 'spotlight' && spriteIndex < importData.spotlights.length ) {
								const spotlightData = importData.spotlights[spriteIndex];
								object.userData.spotlightData = spotlightData;

								// 正确解析亮点名称
								let spriteName = '亮点';
								if ( spotlightData.nameI18n && spotlightData.nameI18n.zhContent ) {
									spriteName = spotlightData.nameI18n.zhContent;
								} else if ( spotlightData.name ) {
									spriteName = spotlightData.name;
								}
								object.name = spriteName;

								// 更新位置（如果有保存的位置信息）
								if ( spotlightData.position ) {
									object.position.fromArray( spotlightData.position );
								}

								// 更新缩放（如果有保存的缩放信息）
								if ( spotlightData.scale ) {
									object.scale.fromArray( spotlightData.scale );
								}

								// 重新创建纹理以确保图标正确显示
								if ( spotlightData.iconCode || spotlightData.iconColor ) {
									console.log(`🔄 重新创建精灵纹理: ${spriteName}, 图标: ${spotlightData.iconCode}, 颜色: ${spotlightData.iconColor}`);

									// 使用相同的createSpotlightTexture函数
									function updateSpotlightTexture( spotlight ) {
										const canvas = document.createElement( 'canvas' );
										const size = 256;
										canvas.width = size;
										canvas.height = size;
										const ctx = canvas.getContext( '2d' );

										// 清空画布并设置透明背景
										ctx.clearRect( 0, 0, size, size );
										ctx.imageSmoothingEnabled = true;
										ctx.imageSmoothingQuality = 'high';

										// 绘制圆角矩形背景
										const rectWidth = size * 0.85;
										const rectHeight = size * 0.85;
										const centerX = size / 2;
										const centerY = size / 2;
										const rectX = (size - rectWidth) / 2;
										const rectY = (size - rectHeight) / 2;
										const cornerRadius = 16;

										// 解析颜色（支持rgba格式）
										let bgColor = spotlight.iconColor || '#3498db';

										// 处理不同颜色格式
										if ( bgColor.startsWith('rgba') ) {
											ctx.fillStyle = bgColor;
										} else if ( bgColor.startsWith('rgb') ) {
											ctx.fillStyle = bgColor;
										} else if ( bgColor.startsWith('#') ) {
											ctx.fillStyle = bgColor;
										} else {
											// 默认蓝色
											ctx.fillStyle = '#3498db';
										}

										// 绘制背景圆角矩形
										ctx.beginPath();
										ctx.roundRect( rectX, rectY, rectWidth, rectHeight, cornerRadius );
										ctx.fill();

										// 添加内阴影效果
										ctx.save();
										ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
										ctx.shadowBlur = 4;
										ctx.shadowOffsetX = 0;
										ctx.shadowOffsetY = 2;

										// 添加边框
										ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
										ctx.lineWidth = 3;
										ctx.stroke();
										ctx.restore();

										// 绘制图标 - 完全参考ResourcePanel.js的智能缩放逻辑
										if ( spotlight.iconCode ) {
											console.log( `🔄 更新精灵图标: ${spotlight.iconCode}` );

											// 查找SVG符号
											const symbol = document.querySelector( `#icon-${spotlight.iconCode}` );
											if ( symbol ) {
												console.log( `✅ 找到SVG符号: ${spotlight.iconCode}` );

												// 尝试使用Path2D绘制SVG路径
												try {
													const pathElements = symbol.querySelectorAll( 'path' );
													if ( pathElements.length > 0 ) {
														console.log( `📐 找到 ${pathElements.length} 个path元素` );

														ctx.fillStyle = 'white';
														ctx.save();

														// 计算所有路径的实际边界框 - 完全参考ResourcePanel.js
														let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
														const paths = [];

														pathElements.forEach( pathElement => {
															const pathData = pathElement.getAttribute( 'd' );
															if ( pathData ) {
																const path2D = new Path2D( pathData );
																paths.push( path2D );

																// 创建临时canvas来测量路径边界
																const tempCanvas = document.createElement('canvas');
																tempCanvas.width = 1024;
																tempCanvas.height = 1024;
																const tempCtx = tempCanvas.getContext('2d');
																tempCtx.fillStyle = 'black';
																tempCtx.fill(path2D);

																// 获取ImageData来计算实际边界
																const imageData = tempCtx.getImageData(0, 0, 1024, 1024);
																const data = imageData.data;

																for (let y = 0; y < 1024; y++) {
																	for (let x = 0; x < 1024; x++) {
																		const index = (y * 1024 + x) * 4;
																		if (data[index + 3] > 0) { // alpha > 0，说明有内容
																			minX = Math.min(minX, x);
																			minY = Math.min(minY, y);
																			maxX = Math.max(maxX, x);
																			maxY = Math.max(maxY, y);
																		}
																	}
																}
															}
														});

														// 如果找到了有效边界
														if (minX !== Infinity && minY !== Infinity && maxX !== -Infinity && maxY !== -Infinity) {
															const pathWidth = maxX - minX;
															const pathHeight = maxY - minY;
															const pathCenterX = minX + pathWidth / 2;
															const pathCenterY = minY + pathHeight / 2;

															// 计算缩放比例，让实际图标内容占满canvas的大部分
															const iconPadding = size * 0.1; // 10%边距
															const availableSize = size - iconPadding * 2;
															const scaleX = availableSize / pathWidth;
															const scaleY = availableSize / pathHeight;
															const scale = Math.min(scaleX, scaleY); // 保持宽高比

															// 移动到canvas中心
															ctx.translate(size / 2, size / 2);
															// 缩放
															ctx.scale(scale, scale);
															// 移动到路径的中心点
															ctx.translate(-pathCenterX, -pathCenterY);

															console.log( `🎨 智能缩放更新: pathBounds=[${minX},${minY},${maxX},${maxY}], pathSize=[${pathWidth},${pathHeight}], scale=${scale}` );
														} else {
															// 回退到原来的方法
															const iconPadding = size * 0.05;
															const iconSize = size - iconPadding * 2;
															const scale = iconSize / 1024;
															ctx.translate(size / 2, size / 2);
															ctx.scale(scale, scale);
															ctx.translate(-512, -512);
															console.log( `🎨 回退缩放更新: scale=${scale}` );
														}

														// 绘制所有路径
														paths.forEach( path2D => {
															ctx.fill( path2D );
														});

														ctx.restore();
														console.log( `✅ 使用智能缩放更新图标: ${spotlight.iconCode}` );
													} else {
														throw new Error( '没有找到path元素' );
													}
												} catch ( error ) {
													console.warn( `⚠️ SVG路径绘制失败: ${error.message}，使用文字备用` );
													// 备用方案：绘制大文字 - 参考ResourcePanel.js
													ctx.fillStyle = 'white';
													const fontSize = Math.floor(size * 0.8); // 字体大小为canvas的80%，更大更清晰
													ctx.font = `bold ${fontSize}px Arial`;
													ctx.textAlign = 'center';
													ctx.textBaseline = 'middle';
													const text = spotlight.iconCode.charAt(0).toUpperCase();
													ctx.fillText( text, centerX, centerY );
													console.log( `📝 绘制备用文字: ${text}, fontSize=${fontSize}` );
												}
											} else {
												console.warn( `⚠️ 未找到SVG符号: ${spotlight.iconCode}，使用文字备用` );
												// 如果没有找到SVG符号，绘制大文字 - 参考ResourcePanel.js
												ctx.fillStyle = 'white';
												const fontSize = Math.floor(size * 0.8); // 字体大小为canvas的80%，更大更清晰
												ctx.font = `bold ${fontSize}px Arial`;
												ctx.textAlign = 'center';
												ctx.textBaseline = 'middle';
												const text = spotlight.iconCode.charAt(0).toUpperCase();
												ctx.fillText( text, centerX, centerY );
											}
										} else {
											// 没有图标代码时绘制默认符号 - 参考ResourcePanel.js
											console.log( '📝 更新精灵：没有iconCode，使用默认图标' );
											ctx.fillStyle = 'white';
											const fontSize = Math.floor(size * 0.8); // 字体大小为canvas的80%，更大更清晰
											ctx.font = `bold ${fontSize}px Arial`;
											ctx.textAlign = 'center';
											ctx.textBaseline = 'middle';
											ctx.fillText( '●', centerX, centerY );
										}

										// 添加高光效果
										const gradient = ctx.createRadialGradient( centerX, centerY - 30, 0, centerX, centerY, rectWidth / 2 );
										gradient.addColorStop( 0, 'rgba(255, 255, 255, 0.4)' );
										gradient.addColorStop( 1, 'rgba(255, 255, 255, 0)' );

										ctx.fillStyle = gradient;
										ctx.beginPath();
										ctx.roundRect( rectX, rectY, rectWidth, rectHeight, cornerRadius );
										ctx.fill();

										return canvas;
									}

									const newCanvas = updateSpotlightTexture( spotlightData );
									const newTexture = new THREE.CanvasTexture( newCanvas );
									newTexture.needsUpdate = true;

									// 更新精灵材质的纹理
									if ( object.material && object.material.map ) {
										object.material.map.dispose(); // 释放旧纹理
										object.material.map = newTexture;
										object.material.needsUpdate = true;
									}
								}

								console.log(`✅ 已更新亮点精灵数据: ${spriteName}`);
								spriteIndex++;
							}
						} );
					}
				}

				// 恢复光源详细设置（延迟执行以确保场景完全加载）
				if ( importData.lights && Array.isArray( importData.lights ) ) {
					console.log(`正在恢复 ${importData.lights.length} 个光源的详细设置...`);

					// 使用setTimeout确保场景完全加载后再恢复光源设置
					setTimeout( () => {
						let restoredCount = 0;
						const processedLights = new Set(); // 记录已处理的光源，避免重复

						importData.lights.forEach( lightInfo => {
							console.log(`🔍 正在查找光源: ${lightInfo.name} (${lightInfo.type}, UUID: ${lightInfo.uuid})`);

							// 尝试多种方式查找光源
							let light = editor.scene.getObjectByProperty( 'uuid', lightInfo.uuid );

							if ( light ) {
								console.log(`✅ 通过UUID找到光源: ${light.name}`);
							}

							// 如果UUID不匹配，尝试按名称和类型查找
							if ( !light ) {
								editor.scene.traverse( function ( object ) {
									if ( object.isLight &&
										 object.type === lightInfo.type &&
										 object.name === lightInfo.name &&
										 !processedLights.has( object.uuid ) ) {
										light = object;
										console.log(`✅ 通过名称和类型找到光源: ${light.name}`);
									}
								} );
							}

							// 如果还是找不到，尝试只按类型查找第一个未处理的匹配光源
							if ( !light ) {
								editor.scene.traverse( function ( object ) {
									if ( object.isLight &&
										 object.type === lightInfo.type &&
										 !processedLights.has( object.uuid ) &&
										 !light ) {
										light = object;
										console.log(`✅ 通过类型找到光源: ${light.name}`);
									}
								} );
							}

							// 如果仍然找不到，尝试创建新的光源
							if ( !light ) {
								console.log(`未找到光源 ${lightInfo.uuid}，尝试创建新光源: ${lightInfo.type}`);
								try {
									// 根据光源类型创建新光源
									switch ( lightInfo.type ) {
										case 'DirectionalLight':
											light = new THREE.DirectionalLight( lightInfo.color || 0xffffff, lightInfo.intensity || 1 );
											break;
										case 'PointLight':
											light = new THREE.PointLight( lightInfo.color || 0xffffff, lightInfo.intensity || 1, lightInfo.distance || 0, lightInfo.decay || 2 );
											break;
										case 'SpotLight':
											light = new THREE.SpotLight( lightInfo.color || 0xffffff, lightInfo.intensity || 1, lightInfo.distance || 0, lightInfo.angle || Math.PI / 3, lightInfo.penumbra || 0, lightInfo.decay || 2 );
											break;
										case 'AmbientLight':
											light = new THREE.AmbientLight( lightInfo.color || 0x404040, lightInfo.intensity || 0.4 );
											break;
										case 'HemisphereLight':
											light = new THREE.HemisphereLight( lightInfo.color || 0xffffbb, lightInfo.groundColor || 0x080820, lightInfo.intensity || 1 );
											break;
										default:
											console.warn(`不支持的光源类型: ${lightInfo.type}`);
											return;
									}

									// 设置基本属性
									light.name = lightInfo.name || lightInfo.type;
									if ( lightInfo.position ) {
										light.position.fromArray( lightInfo.position );
									}
									if ( lightInfo.rotation ) {
										light.rotation.fromArray( lightInfo.rotation );
									}

									// 添加到场景
									editor.addObject( light );
									console.log(`✅ 已创建新光源: ${light.name} (${light.type})`);
								} catch ( error ) {
									console.error(`创建光源失败:`, error);
									return;
								}
							}

							if ( light && light.isLight && !processedLights.has( light.uuid ) ) {
								console.log(`找到光源进行恢复:`, light.name, light.type, light.uuid);
								processedLights.add( light.uuid ); // 标记为已处理
								// 恢复光源属性
								if ( lightInfo.color !== undefined ) {
									light.color.setHex( lightInfo.color );
								}
								if ( lightInfo.intensity !== undefined ) {
									light.intensity = lightInfo.intensity;
								}
								if ( lightInfo.distance !== undefined ) {
									light.distance = lightInfo.distance;
								}
								if ( lightInfo.decay !== undefined ) {
									light.decay = lightInfo.decay;
								}
								if ( lightInfo.angle !== undefined ) {
									light.angle = lightInfo.angle;
								}
								if ( lightInfo.penumbra !== undefined ) {
									light.penumbra = lightInfo.penumbra;
								}
								if ( lightInfo.castShadow !== undefined ) {
									light.castShadow = lightInfo.castShadow;
								}

								// 恢复阴影设置
								if ( lightInfo.shadow && light.shadow ) {
									if ( lightInfo.shadow.mapSize ) {
										light.shadow.mapSize.width = lightInfo.shadow.mapSize.width;
										light.shadow.mapSize.height = lightInfo.shadow.mapSize.height;
									}
									if ( lightInfo.shadow.camera ) {
										const shadowCamera = light.shadow.camera;
										if ( lightInfo.shadow.camera.near !== undefined ) shadowCamera.near = lightInfo.shadow.camera.near;
										if ( lightInfo.shadow.camera.far !== undefined ) shadowCamera.far = lightInfo.shadow.camera.far;
										if ( lightInfo.shadow.camera.fov !== undefined ) shadowCamera.fov = lightInfo.shadow.camera.fov;
										if ( lightInfo.shadow.camera.left !== undefined ) shadowCamera.left = lightInfo.shadow.camera.left;
										if ( lightInfo.shadow.camera.right !== undefined ) shadowCamera.right = lightInfo.shadow.camera.right;
										if ( lightInfo.shadow.camera.top !== undefined ) shadowCamera.top = lightInfo.shadow.camera.top;
										if ( lightInfo.shadow.camera.bottom !== undefined ) shadowCamera.bottom = lightInfo.shadow.camera.bottom;
										shadowCamera.updateProjectionMatrix();
									}
									if ( lightInfo.shadow.bias !== undefined ) light.shadow.bias = lightInfo.shadow.bias;
									if ( lightInfo.shadow.normalBias !== undefined ) light.shadow.normalBias = lightInfo.shadow.normalBias;
									if ( lightInfo.shadow.radius !== undefined ) light.shadow.radius = lightInfo.shadow.radius;
								}

								restoredCount++;
								console.log(`已恢复光源 "${light.name}" 的设置`);
							} else if ( !light ) {
								console.warn(`❌ 未找到光源: ${lightInfo.name} (${lightInfo.type}, UUID: ${lightInfo.uuid})`);
							} else {
								console.log(`⚠️ 光源 "${light.name}" 已经被处理过，跳过`);
							}
						} );

						console.log(`光源恢复完成: ${restoredCount}/${importData.lights.length}`);

						// 触发渲染更新
						try {
							if ( editor.signals && editor.signals.sceneGraphChanged ) {
								editor.signals.sceneGraphChanged.dispatch();
							}
							// 不触发 objectChanged 信号，因为它需要特定的对象参数
						} catch ( error ) {
							console.error('触发渲染更新信号失败:', error);
						}
					}, 100 );
				}

				// 触发场景更新信号（延迟执行确保所有对象都已加载）
				setTimeout( () => {
					try {
						if ( editor.signals && editor.signals.sceneGraphChanged ) {
							editor.signals.sceneGraphChanged.dispatch();
						}
						// 不在这里触发相机信号，因为相机恢复是异步的
					} catch ( error ) {
						console.error('触发场景更新信号失败:', error);
					}
				}, 50 );

				// 显示详细的成功消息
				const spotlightCount = importData.spotlights ? importData.spotlights.length : 0;
				const lightCount = importData.lights ? importData.lights.length : 0;
				const successMessage = `场景导入成功！\n文件: ${file.name}\n导入对象: ${importedObjectCount}个\n恢复光源: ${lightCount}个\n恢复亮点: ${spotlightCount}个\n文件大小: ${Math.round(file.size / 1024)}KB\n\n💡 注意：左侧看板的亮点数据保持不变`;
				console.log(successMessage);
				console.log('🔒 数据分离说明：');
				console.log('  - 场景中的亮点精灵：使用导入的亮点数据');
				console.log('  - 左侧看板列表：继续使用API请求的亮点数据');
				console.log('  - 两者互不影响，保持数据源的独立性');
				// alert( successMessage );

			}

		} catch ( error ) {

			console.error('导入场景失败:', error);

			let errorMessage = '导入场景失败: ';
			if ( error.name === 'SyntaxError' ) {
				errorMessage += 'JSON文件格式错误，请检查文件是否损坏';
			} else if ( error.message.includes('parse') ) {
				errorMessage += '场景数据解析失败，可能包含不支持的对象类型';
			} else {
				errorMessage += error.message;
			}

			errorMessage += '\n\n请确保导入的是从Three.js编辑器导出的有效场景文件。';
			alert( errorMessage );

		} finally {

			importSceneForm.reset();

		}

	} );

	importSceneForm.appendChild( importSceneInput );

	option = new UIRow();
	option.setClass( 'option' );
	option.setTextContent( '📥 导入完整场景' );
	option.onClick( function () {

		importSceneInput.click();

	} );
	options.add( option );

	// Export

	const fileExportSubmenuTitle = new UIRow().setTextContent( strings.getKey( 'menubar/file/export' ) ).addClass( 'option' ).addClass( 'submenu-title' );
	fileExportSubmenuTitle.onMouseOver( function () {

		const { top, right } = this.dom.getBoundingClientRect();
		const { paddingTop } = getComputedStyle( this.dom );
		fileExportSubmenu.setLeft( right + 'px' );
		fileExportSubmenu.setTop( top - parseFloat( paddingTop ) + 'px' );
		fileExportSubmenu.setDisplay( 'block' );

	} );
	fileExportSubmenuTitle.onMouseOut( function () {

		fileExportSubmenu.setDisplay( 'none' );

	} );
	options.add( fileExportSubmenuTitle );

	const fileExportSubmenu = new UIPanel().setPosition( 'fixed' ).addClass( 'options' ).setDisplay( 'none' );
	fileExportSubmenuTitle.add( fileExportSubmenu );

	// Export DRC

	option = new UIRow();
	option.setClass( 'option' );
	option.setTextContent( 'DRC' );
	option.onClick( async function () {

		const object = editor.selected;

		if ( object === null || object.isMesh === undefined ) {

			alert( strings.getKey( 'prompt/file/export/noMeshSelected' ) );
			return;

		}

		const { DRACOExporter } = await import( 'three/addons/exporters/DRACOExporter.js' );

		const exporter = new DRACOExporter();

		const options = {
			decodeSpeed: 5,
			encodeSpeed: 5,
			encoderMethod: DRACOExporter.MESH_EDGEBREAKER_ENCODING,
			quantization: [ 16, 8, 8, 8, 8 ],
			exportUvs: true,
			exportNormals: true,
			exportColor: object.geometry.hasAttribute( 'color' )
		};

		// TODO: Change to DRACOExporter's parse( geometry, onParse )?
		const result = exporter.parse( object, options );
		saveArrayBuffer( result, 'model.drc' );

	} );
	fileExportSubmenu.add( option );

	// Export GLB

	option = new UIRow();
	option.setClass( 'option' );
	option.setTextContent( 'GLB' );
	option.onClick( async function () {

		const scene = editor.scene;
		const animations = getAnimations( scene );

		const optimizedAnimations = [];

		for ( const animation of animations ) {

			optimizedAnimations.push( animation.clone().optimize() );

		}

		const { GLTFExporter } = await import( 'three/addons/exporters/GLTFExporter.js' );

		const exporter = new GLTFExporter();

		exporter.parse( scene, function ( result ) {

			saveArrayBuffer( result, 'scene.glb' );

		}, undefined, { binary: true, animations: optimizedAnimations } );

	} );
	fileExportSubmenu.add( option );

	// Export GLTF

	option = new UIRow();
	option.setClass( 'option' );
	option.setTextContent( 'GLTF' );
	option.onClick( async function () {

		const scene = editor.scene;
		const animations = getAnimations( scene );

		const optimizedAnimations = [];

		for ( const animation of animations ) {

			optimizedAnimations.push( animation.clone().optimize() );

		}

		const { GLTFExporter } = await import( 'three/addons/exporters/GLTFExporter.js' );

		const exporter = new GLTFExporter();

		exporter.parse( scene, function ( result ) {

			saveString( JSON.stringify( result, null, 2 ), 'scene.gltf' );

		}, undefined, { animations: optimizedAnimations } );


	} );
	fileExportSubmenu.add( option );

	// Export OBJ

	option = new UIRow();
	option.setClass( 'option' );
	option.setTextContent( 'OBJ' );
	option.onClick( async function () {

		const object = editor.selected;

		if ( object === null ) {

			alert( strings.getKey( 'prompt/file/export/noObjectSelected' ) );
			return;

		}

		const { OBJExporter } = await import( 'three/addons/exporters/OBJExporter.js' );

		const exporter = new OBJExporter();

		saveString( exporter.parse( object ), 'model.obj' );

	} );
	fileExportSubmenu.add( option );

	// Export PLY (ASCII)

	option = new UIRow();
	option.setClass( 'option' );
	option.setTextContent( 'PLY' );
	option.onClick( async function () {

		const { PLYExporter } = await import( 'three/addons/exporters/PLYExporter.js' );

		const exporter = new PLYExporter();

		exporter.parse( editor.scene, function ( result ) {

			saveArrayBuffer( result, 'model.ply' );

		} );

	} );
	fileExportSubmenu.add( option );

	// Export PLY (BINARY)

	option = new UIRow();
	option.setClass( 'option' );
	option.setTextContent( 'PLY (BINARY)' );
	option.onClick( async function () {

		const { PLYExporter } = await import( 'three/addons/exporters/PLYExporter.js' );

		const exporter = new PLYExporter();

		exporter.parse( editor.scene, function ( result ) {

			saveArrayBuffer( result, 'model-binary.ply' );

		}, { binary: true } );

	} );
	fileExportSubmenu.add( option );

	// Export STL (ASCII)

	option = new UIRow();
	option.setClass( 'option' );
	option.setTextContent( 'STL' );
	option.onClick( async function () {

		const { STLExporter } = await import( 'three/addons/exporters/STLExporter.js' );

		const exporter = new STLExporter();

		saveString( exporter.parse( editor.scene ), 'model.stl' );

	} );
	fileExportSubmenu.add( option );

	// Export STL (BINARY)

	option = new UIRow();
	option.setClass( 'option' );
	option.setTextContent( 'STL (BINARY)' );
	option.onClick( async function () {

		const { STLExporter } = await import( 'three/addons/exporters/STLExporter.js' );

		const exporter = new STLExporter();

		saveArrayBuffer( exporter.parse( editor.scene, { binary: true } ), 'model-binary.stl' );

	} );
	fileExportSubmenu.add( option );

	// Export USDZ

	option = new UIRow();
	option.setClass( 'option' );
	option.setTextContent( 'USDZ' );
	option.onClick( async function () {

		const { USDZExporter } = await import( 'three/addons/exporters/USDZExporter.js' );

		const exporter = new USDZExporter();

		saveArrayBuffer( await exporter.parseAsync( editor.scene ), 'model.usdz' );

	} );
	fileExportSubmenu.add( option );

	//

	fileExportSubmenu.add( new UIHorizontalRule() );

	// Export Scene JSON (一键导出场景)

	option = new UIRow();
	option.setClass( 'option' );
	option.setTextContent( '🎯 导出完整场景' );
	option.onClick( function () {

		try {
			// 检查场景是否为空
			if ( editor.scene.children.length === 0 ) {
				if ( !confirm( '当前场景为空，是否仍要导出？' ) ) {
					return;
				}
			}

			// 显示导出进度提示
			console.log('正在导出场景...');

			// 获取完整的场景数据，包括所有模型和配置
			const sceneData = editor.scene.toJSON();

			// 统计场景信息
			const objectCount = editor.scene.children.length;
			const meshCount = editor.scene.children.filter(child => child.isMesh).length;
			const lightCount = editor.scene.children.filter(child => child.isLight).length;
			const spriteCount = editor.scene.children.filter(child => child.isSprite).length;

			// 调试：显示场景中的所有对象
			console.log('导出时场景中的对象:');
			editor.scene.children.forEach( (child, index) => {
				console.log(`  ${index + 1}. ${child.name} (${child.type}) - UUID: ${child.uuid}`);
			} );

			// 收集亮点数据
			const spotlightData = [];

			// 尝试多种方式获取亮点数据
			if ( window.spotlightData && Array.isArray( window.spotlightData ) ) {
				spotlightData.push( ...window.spotlightData );
				console.log(`从 window.spotlightData 收集到 ${window.spotlightData.length} 个亮点`);
			}

			if ( editor.spotlightData && Array.isArray( editor.spotlightData ) ) {
				spotlightData.push( ...editor.spotlightData );
				console.log(`从 editor.spotlightData 收集到 ${editor.spotlightData.length} 个亮点`);
			}

			if ( window.parent && window.parent.spotlightData && Array.isArray( window.parent.spotlightData ) ) {
				spotlightData.push( ...window.parent.spotlightData );
				console.log(`从 window.parent.spotlightData 收集到 ${window.parent.spotlightData.length} 个亮点`);
			}

			// 从场景中查找亮点对象（多种可能的标识方式）
			editor.scene.traverse( function ( object ) {
				if ( object.userData ) {
					// 检查多种可能的亮点标识
					if ( object.userData.type === 'spotlight' ||
						 object.userData.isSpotlight === true ||
						 object.userData.spotlightData ) {

						const spotlightInfo = object.userData.spotlightData || object.userData;
						// 保存位置信息
						if ( object.position ) {
							spotlightInfo.position = object.position.toArray();
						}
						spotlightData.push( spotlightInfo );
						console.log(`从场景对象收集到亮点:`, object.name, spotlightInfo);
					}
				}

				// 检查是否是亮点精灵对象
				if ( object.isSprite && object.userData && object.userData.type === 'spotlight' ) {
					const spotlightInfo = { ...( object.userData.spotlightData || object.userData ) };
					// 保存精灵的位置信息
					if ( object.position ) {
						spotlightInfo.position = object.position.toArray();
					}
					// 保存精灵的缩放信息
					if ( object.scale ) {
						spotlightInfo.scale = object.scale.toArray();
					}
					spotlightData.push( spotlightInfo );
					console.log(`从精灵对象收集到亮点:`, object.name, spotlightInfo);
				}
			} );

			// 去重处理
			const uniqueSpotlights = [];
			const seenCodes = new Set();
			spotlightData.forEach( spotlight => {
				const code = spotlight.code || spotlight.id || spotlight.uuid;
				if ( code && !seenCodes.has( code ) ) {
					seenCodes.add( code );
					uniqueSpotlights.push( spotlight );
				} else if ( !code ) {
					// 没有唯一标识的亮点也保留
					uniqueSpotlights.push( spotlight );
				}
			} );

			console.log(`收集到 ${spotlightData.length} 个亮点数据，去重后 ${uniqueSpotlights.length} 个`);

			// 收集光源数据
			const lightData = [];
			console.log('开始收集光源数据...');
			editor.scene.traverse( function ( object ) {
				if ( object.isLight ) {
					console.log(`发现光源:`, object.name, object.type);
					const lightInfo = {
						uuid: object.uuid,
						name: object.name,
						type: object.type,
						position: object.position.toArray(),
						rotation: object.rotation.toArray(),
						scale: object.scale.toArray(),
						color: object.color ? object.color.getHex() : 0xffffff,
						intensity: object.intensity || 1,
						distance: object.distance || 0,
						decay: object.decay || 1,
						angle: object.angle || Math.PI / 3,
						penumbra: object.penumbra || 0,
						castShadow: object.castShadow || false,
						shadow: object.shadow ? {
							mapSize: {
								width: object.shadow.mapSize.width,
								height: object.shadow.mapSize.height
							},
							camera: {
								near: object.shadow.camera.near,
								far: object.shadow.camera.far,
								fov: object.shadow.camera.fov,
								left: object.shadow.camera.left,
								right: object.shadow.camera.right,
								top: object.shadow.camera.top,
								bottom: object.shadow.camera.bottom
							},
							bias: object.shadow.bias,
							normalBias: object.shadow.normalBias,
							radius: object.shadow.radius
						} : null
					};
					lightData.push( lightInfo );
				}
			} );

			console.log(`收集到 ${lightData.length} 个光源数据`);

			// 添加编辑器的额外信息
			const exportData = {
				metadata: {
					version: 4.5,
					type: "Scene",
					generator: "Three.js Editor",
					exportTime: new Date().toISOString(),
					editorVersion: "1.0.0",
					objectCount: objectCount,
					meshCount: meshCount,
					lightCount: lightCount,
					spriteCount: spriteCount,
					spotlightCount: uniqueSpotlights.length,
					description: "完整场景导出，包含所有模型、材质、灯光、亮点和相机设置"
				},
				scene: sceneData,
				// 保存相机信息
				camera: {
					position: editor.camera.position.toArray(),
					rotation: editor.camera.rotation.toArray(),
					zoom: editor.camera.zoom || 1,
					type: editor.camera.type,
					fov: editor.camera.fov || 50,
					near: editor.camera.near || 0.1,
					far: editor.camera.far || 2000
				},
				// 保存编辑器设置
				settings: {
					viewportShading: editor.viewportShading || 'solid',
					gridVisible: editor.grid ? editor.grid.visible : true,
					helpersVisible: true,
					backgroundColor: editor.scene.background ? editor.scene.background.getHex() : 0xaaaaaa
				},
				// 保存亮点数据
				spotlights: uniqueSpotlights,
				// 保存光源详细数据
				lights: lightData
			};

			// 生成用户友好的文件名
			const now = new Date();
			const dateStr = now.getFullYear() +
				String(now.getMonth() + 1).padStart(2, '0') +
				String(now.getDate()).padStart(2, '0');
			const timeStr = String(now.getHours()).padStart(2, '0') +
				String(now.getMinutes()).padStart(2, '0') +
				String(now.getSeconds()).padStart(2, '0');

			const filename = `3d_scene_${dateStr}_${timeStr}.json`;

			// 导出JSON文件
			const jsonString = JSON.stringify( exportData, null, 2 );
			const fileSizeKB = Math.round(jsonString.length / 1024);

			saveString( jsonString, filename );

			// 显示详细的成功消息
			console.log(`场景导出成功:
				文件名: ${filename}
				文件大小: ${fileSizeKB}KB
				对象数量: ${objectCount}
				网格数量: ${meshCount}
				光源数量: ${lightCount}
				精灵数量: ${spriteCount}
				亮点数量: ${uniqueSpotlights.length}
				导出时间: ${new Date().toLocaleString()}`);

			// 调试信息
			console.log('导出的亮点数据:', uniqueSpotlights);
			console.log('导出的光源数据:', lightData);

			// 简化的用户提示
			alert( `场景导出成功！\n文件名: ${filename}\n对象数量: ${objectCount}\n网格数量: ${meshCount}\n光源数量: ${lightCount}\n精灵数量: ${spriteCount}\n亮点数量: ${uniqueSpotlights.length}\n文件大小: ${fileSizeKB}KB` );

		} catch ( error ) {

			console.error('导出场景失败:', error);
			alert( `导出场景失败: ${error.message}\n\n请检查场景中是否有无效的对象或材质。` );

		}

	} );
	fileExportSubmenu.add( option );

	//

	function getAnimations( scene ) {

		const animations = [];

		scene.traverse( function ( object ) {

			animations.push( ... object.animations );

		} );

		return animations;

	}

	return container;

}

export { MenubarFile };
