import request from '@/utils/request';

//对象存储 文本上传
export function object_uploadText(param) {
  return request({
    url: '/platform-api/assets/v1/data/object/upload/text',
    method: 'POST',
    data: param,
  });
}

//对象存储 文件上传
export function object_uploadFile(param) {
  return request({
    url: '/platform-api/assets/v1/data/object/upload/file',
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8' },
    timeout: 1000*15,
    data: param,
  });
}

//查询 对象存储 List
export function object_findList(param) {
  return request({
    url: '/platform-api/assets/v1/data/object/findList',
    method: 'POST',
    data: param,
  });
}

//查询 对象存储 详情
export function object_findByCode(code) {
  return request({
    url: `/platform-api/assets/v1/data/object/find/${code}`,
    method: 'POST',
  });
}

//删除 对象存储
export function object_delete(codes) {
  return request({
    url: `/platform-api/assets/v1/data/object/delete/${codes}`,
    method: 'POST',
  });
}

//更新 对象存储 文本
export function object_updateText(param) {
  return request({
    url: '/platform-api/assets/v1/data/object/update/text',
    method: 'POST',
    data: param,
  });
}

//更新 对象存储 文件
export function object_updateFile(param) {
  return request({
    url: '/platform-api/assets/v1/data/object/update/file',
    method: 'POST',
    data: param,
  });
}

//更新 对象存储 批量修改对象
export function object_updateWithBatch(param) {
  return request({
    url: '/platform-api/assets/v1/data/object/updateWithBatch',
    method: 'POST',
    data: param,
  });
}
