<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <el-form :inline="true" :model="queryParams" class="search-bar" v-show="showSearch">
      <el-form-item label="工厂" :style="{width: '200px'}">
        <el-select v-model="queryParams.factoryCode" placeholder="请选择工厂" clearable @change="handleFactoryChange">
          <el-option v-for="item in factoryOptions" :key="item.code" :label="item.name" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item label="区域" :style="{width: '200px'}">
        <el-select v-model="queryParams.regionCode" placeholder="请选择区域" clearable @change="handleRegionChange">
          <el-option v-for="item in regionOptions" :key="item.code" :label="item.name" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item label="楼栋" :style="{width: '200px'}">
        <el-select
          v-model="queryParams.buildingCode"
          placeholder="请选择楼栋"
          clearable
          @change="handleBuildingChange"
          :disabled="queryParams.regionCode === '0'"
        >
          <el-option v-for="item in buildingOptions" :key="item.code" :label="item.name" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item label="楼层" :style="{width: '200px'}">
        <el-select
          v-model="queryParams.floorCode"
          placeholder="请选择楼层"
          clearable
          :disabled="queryParams.regionCode === '0'"
        >
          <el-option v-for="item in floorOptions" :key="item.code" :label="item.name" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item label="模型名称">
        <el-input v-model="queryParams.name" placeholder="请输入模型名称" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['api:model:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['api:model:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          :icon="viewMode === 'table' ? 'Grid' : 'List'"
          @click="toggleViewMode"
        >{{ viewMode === 'table' ? '卡片视图' : '表格视图' }}</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 表格视图 -->
    <el-table v-if="viewMode === 'table'" v-loading="loading" :data="modelList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="唯一ID" align="center" prop="id" />
      <el-table-column label="图片描述" align="center" prop="desc" />
      <el-table-column label="模型名称" align="center" prop="name" />
      <el-table-column label="文件后缀" align="center" prop="suffix" />
      <el-table-column label="模型缩略图" align="center" prop="modelImg" width="100">
        <template #default="scope">
          <image-preview :src="scope.row.modelImg" :width="50" :height="50"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="success" icon="Edit" @click="handle3DEdit(scope.row)" v-hasPermi="['api:model:edit']">3D编辑</el-button>
          <el-button link type="primary" icon="Setting" @click="handleUpdate(scope.row)" v-hasPermi="['api:model:edit']">编辑</el-button>
          <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['api:model:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 卡片视图 -->
    <div v-else class="card-container">
      <el-row :gutter="20">
        <el-col :span="6" v-for="item in modelList" :key="item.id" class="card-item">
          <el-card class="model-card" shadow="hover">
            <div class="card-wrapper" @mouseenter="item.hover = true" @mouseleave="item.hover = false">
              <div class="card-model-container">
                <model-viewer
                  v-if="item.objectUrl"
                  :src="'/platform-api/' + item.objectUrl"
                  alt="3D 模型"
                  class="model-viewer"
                  auto-rotate
                  camera-controls
                  disable-zoom
                  touch-action="pan-y"
                  exposure="1.0"
                  :style="{ backgroundColor: item.hover ? '#ffffff' : '#182f4a' }"
                  @error="handleModelError"
                ></model-viewer>
                <div v-else class="card-model-placeholder">
                  <el-icon :size="60" color="#c0c4cc">
                    <Box />
                  </el-icon>
                  <div class="placeholder-text">暂无3D模型</div>
                </div>

                <!-- 操作按钮遮罩层 -->
                <div v-if="item.hover" class="overlay">
                  <div
                    v-if="item.objectUrl"
                    class="flex-box edt_sam"
                    @click="handlePreview('/platform-api/' + item.objectUrl)"
                  >
                    <el-icon><View /></el-icon>
                    <div>预览</div>
                  </div>
                  <div class="flex-box edt_sam" @click="handle3DEdit(item)">
                    <el-icon><Edit /></el-icon>
                    <div>3D编辑</div>
                  </div>
                  <div class="flex-box edt_sam" @click="handleUpdate(item)">
                    <el-icon><Setting /></el-icon>
                    <div>编辑</div>
                  </div>
                  <div class="flex-box edt_sam" @click="handleDelete(item)">
                    <el-icon><Delete /></el-icon>
                    <div>删除</div>
                  </div>
                </div>
              </div>
              <div class="card-info">
                <div class="model-name">{{ item.name || '未命名模型' }}</div>
                <div class="model-suffix">.{{ item.suffix || 'unknown' }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <pagination
      v-show="total>0"
      :total="total"
      v-model:page-num="queryParams.page"
      v-model:page-size="queryParams.size"
      @pagination="handlePaginationEvent"
    />

    <!-- 3D模型预览对话框 -->
    <el-dialog v-model="modelPreviewVisible" title="3D 模型预览" width="60%" append-to-body>
      <model-viewer
        v-if="previewModelUrl"
        :src="previewModelUrl"
        alt="3D 模型"
        camera-controls
        auto-rotate
        style="width: 100%; height: 500px; --poster-color: #0f1b2a; background-color: #182f4a"
        shadow-intensity="1"
        exposure="0.8"
        environment-image="neutral"
      >
      </model-viewer>
    </el-dialog>

    <!-- 添加或修改业务 holo工厂3D模型对话框 -->
    <el-dialog :title="title" v-model="open" width="50vw" append-to-body>
      <el-form ref="modelRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="工厂" prop="factoryCode">
          <el-select
            v-model="form.factoryCode"
            placeholder="请选择工厂"
            clearable
            style="width: 100%"
            @change="handleFormFactoryChange"
          >
            <el-option v-for="item in factoryOptions" :key="item.code" :label="item.name" :value="item.code" />
          </el-select>
        </el-form-item>
        <el-form-item label="区域" prop="regionCode">
          <el-select
            v-model="form.regionCode"
            placeholder="请选择区域"
            clearable
            style="width: 100%"
            @change="handleFormRegionChange"
          >
            <el-option v-for="item in formRegionOptions" :key="item.code" :label="item.name" :value="item.code" />
          </el-select>
        </el-form-item>
        <el-form-item label="楼栋" prop="buildingCode">
          <el-select
            v-model="form.buildingCode"
            placeholder="请选择楼栋"
            clearable
            style="width: 100%"
            @change="handleFormBuildingChange"
            :disabled="form.regionCode === '0'"
          >
            <el-option v-for="item in formBuildingOptions" :key="item.code" :label="item.name" :value="item.code" />
          </el-select>
        </el-form-item>
        <el-form-item label="楼层" prop="floorCode">
          <el-select
            v-model="form.floorCode"
            placeholder="请选择楼层"
            clearable
            style="width: 100%"
            :disabled="form.regionCode === '0'"
          >
            <el-option v-for="item in formFloorOptions" :key="item.code" :label="item.name" :value="item.code" />
          </el-select>
        </el-form-item>
        <el-form-item label="模型名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入模型名称" />
        </el-form-item>
        <el-form-item label="模型描述" prop="desc">
          <el-input v-model="form.desc" placeholder="请输入模型描述" />
        </el-form-item>
        <el-form-item v-if="!isEditMode" label="模型类型" prop="isSystem">
          <el-select v-model="form.isSystem" placeholder="请选择模型类型" style="width: 100%">
            <el-option label="用户模型" :value="0" />
            <el-option label="系统模型" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="!isEditMode" label="文件后缀" prop="suffix">
          <el-select v-model="form.suffix" placeholder="请选择文件后缀" style="width: 100%">
            <el-option label="GLB" value="glb" />
            <el-option label="GLTF" value="gltf" />
            <el-option label="FBX" value="fbx" />
            <el-option label="OBJ" value="obj" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否首图" prop="startStatus">
          <el-switch
            v-model="form.startStatus"
            :active-value="1"
            :inactive-value="0"
            active-text="是"
            inactive-text="否"
          />
        </el-form-item>
        <el-form-item v-if="!isEditMode" label="公版类型" prop="isCommon">
          <el-select v-model="form.isCommon" placeholder="请选择公版类型" style="width: 100%">
            <el-option label="用户上传" :value="0" />
            <el-option label="用户配置公版" :value="1" />
            <el-option label="用户配置非公版" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="!isEditMode" label="模型文件" prop="objCode">
          <file-upload v-model="form.objCode"/>
        </el-form-item>
        <el-form-item v-if="!isEditMode" label="模型缩略图" prop="modelImg">
          <image-upload v-model="form.modelImg"/>
        </el-form-item>
        <el-form-item label="备注信息" prop="remark">
          <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Model">
import { getCurrentInstance, watch, onMounted, onActivated, nextTick, onBeforeUnmount } from 'vue';
import { useRoute } from 'vue-router';
import { listModel, getModel, delModel, addModel, updateModel } from "@/api/holo/model";
import { ElMessage, ElMessageBox } from 'element-plus';
import { Box, View, Edit, Delete, Setting } from '@element-plus/icons-vue';
import Pagination from '@/components/Pagination/index'
import { getUserFactory } from '@/api/holo/factory';
import { getRegionList, findBuildingListWithFloor, getRegion } from '@/api/floor';
import { getSceneByCode } from '@/api/holo/scene';

const { proxy } = getCurrentInstance();
const route = useRoute();
const QUERY_CACHE_KEY = 'model_query_cache';

const modelList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const isEditMode = ref(false); // 新增：用于区分新增和编辑模式

// 新增的数据
const factoryOptions = ref([]);
const regionOptions = ref([]);
const buildingOptions = ref([]);
const floorOptions = ref([]);

// 弹窗专用的选项数据
const formRegionOptions = ref([]);
const formBuildingOptions = ref([]);
const formFloorOptions = ref([]);

const viewMode = ref('table'); // 视图模式：table-表格，card-卡片

// 预览相关
const modelPreviewVisible = ref(false);
const previewModelUrl = ref(null);

const queryParams = ref({
  page: 1,
  size: 10,
  factoryCode: '',
  regionCode: '',
  buildingCode: '',
  floorCode: '',
  name: '',
  orderNum: '',
  orderByColumn: 'id', // 排序字段
  isAsc: 'asc', // 排序方式
  isSystem:0,

});

const data = reactive({
  form: {},
  rules: {
    groupCode: [
      { required: true, message: "集团code不能为空", trigger: "blur" }
    ],
    organizeCode: [
      { required: true, message: "组织code不能为空", trigger: "blur" }
    ],
    objCode: [
      { required: true, message: "对象存储code不能为空", trigger: "blur" }
    ],
    code: [
      { required: true, message: "表code不能为空", trigger: "blur" }
    ],
    name: [
      { required: true, message: "模型名称不能为空", trigger: "blur" }
    ],
    factoryCode: [
      { required: true, message: "工厂code不能为空", trigger: "blur" }
    ],
    isSystem: [
      { required: true, message: "0为用户上传1为系统模型不能为空", trigger: "blur" }
    ],
    startStatus: [
      { required: true, message: "工厂首个展示的模型0：否 1：是不能为空", trigger: "change" }
    ],
    isCommon: [
      { required: true, message: "0用户上传1为用户配置好的公版2用户配置的非公版不能为空", trigger: "blur" }
    ],
    suffix: [
      { required: true, message: "文件后缀不能为空", trigger: "blur" }
    ],
    deleteStatus: [
      { required: true, message: "删除状态 0未删除 1已删除不能为空", trigger: "change" }
    ],
  }
});

const { form, rules } = toRefs(data);

/** 查询业务 holo工厂3D模型列表 */
function getList() {
  loading.value = true;
  listModel(queryParams.value).then(response => {
    modelList.value = response.data.list;
    total.value = response.data.total;
    loading.value = false;

    // 调试：打印模型数据结构
    console.log('模型列表数据:', modelList.value);
    if (modelList.value.length > 0) {
      console.log('第一个模型的字段:', Object.keys(modelList.value[0]));
      console.log('第一个模型的完整数据:', modelList.value[0]);
    }
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    groupCode: null,
    organizeCode: null,
    objCode: null,
    code: null,
    desc: null,
    name: null,
    buildingCode: null,
    floorCode: null,
    regionCode: null,
    factoryCode: null,
    isSystem: null,
    startStatus: null,
    isCommon: null,
    modelType: null,
    suffix: null,
    modelImg: null,
    deleteStatus: null,
    createTime: null,
    updateTime: null,
    createUserCode: null,
    updateUserCode: null,
    remark: null
  };
  proxy.resetForm("modelRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.page = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  queryParams.value = {
    page: 1,
    size: 10,
    factoryCode: '',
    regionCode: '',
    buildingCode: '',
    floorCode: '',
    name: '',
    orderNum: '',
    orderByColumn: '',
    isAsc: '',
  };
  regionOptions.value = [];
  buildingOptions.value = [];
  floorOptions.value = [];
  getList();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  // 在当前浏览器中打开新的tab页，使用全屏3D编辑器
  const editorUrl = window.location.origin + '/3d/editor/fullscreen';
  window.open(editorUrl, '_blank');
}

/** 新增模型弹窗操作 */
function handleAddModel() {
  reset();
  // 清空弹窗选项数据
  formRegionOptions.value = [];
  formBuildingOptions.value = [];
  formFloorOptions.value = [];
  isEditMode.value = false; // 设置为新增模式
  open.value = true;
  title.value = "添加业务 holo工厂3D模型";
}

/** 修改按钮操作 */
async function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  try {
    const response = await getModel(_id);
    form.value = response.data;

    // 如果有工厂代码，加载对应的区域、楼栋、楼层选项
    if (form.value.factoryCode) {
      await handleFormFactoryChange();

      if (form.value.regionCode) {
        await handleFormRegionChange();

        if (form.value.buildingCode) {
          handleFormBuildingChange();
        }
      }
    }

    isEditMode.value = true; // 设置为编辑模式
    open.value = true;
    title.value = "修改业务 holo工厂3D模型";
  } catch (error) {
    console.error('获取模型数据失败:', error);
    ElMessage.error('获取模型数据失败');
  }
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["modelRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateModel(form.value).then(response => {
          ElMessage.success("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addModel(form.value).then(response => {
          ElMessage.success("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  ElMessageBox.confirm('是否确认删除业务 holo工厂3D模型编号为"' + _ids + '"的数据项？').then(function() {
    return delModel(_ids);
  }).then(() => {
    getList();
    ElMessage.success("删除成功");
  }).catch(() => {});
}

/** 处理分页回调事件 */
function handlePaginationEvent(pagination) {
  const { pageNum, pageSize } = pagination;
  queryParams.value.page = pageNum;
  queryParams.value.size = pageSize;
  getList();
}

/** 切换视图模式 */
function toggleViewMode() {
  viewMode.value = viewMode.value === 'table' ? 'card' : 'table';
}

/**
 * 预览3D模型
 * @param url
 */
function handlePreview(url) {
  console.log('预览模型URL:', url);
  previewModelUrl.value = url;
  modelPreviewVisible.value = true;
}

/**
 * 处理模型加载错误
 * @param event
 */
function handleModelError(event) {
  console.error('3D模型加载失败:', event);
}

/**
 * 3D编辑功能
 * @param row 模型数据
 */
async function handle3DEdit(row) {
  console.log('进入3D编辑模式:', row);

  try {
    // 使用模型的code调用getScene获取场景信息
    const sceneResponse = await getSceneByCode(row.code);
    console.log('场景数据响应:', sceneResponse);

    if (sceneResponse && sceneResponse.data && sceneResponse.data.objUrl) {
      // 获取到场景json文件的路径
      const sceneJsonUrl = `http://localhost:81/platform-api/${sceneResponse.data.objUrl}`;
      console.log('场景JSON文件路径:', sceneJsonUrl);
      console.log('完整的场景响应数据:', sceneResponse.data);

      // 准备传递给编辑器的数据
      const modelData = {
        id: row.id,
        name: row.name,
        code: row.code,
        objCode: row.objCode,
        objectUrl: row.objectUrl,
        modelImg: row.modelImg,
        factoryCode: row.factoryCode,
        regionCode: row.regionCode,
        buildingCode: row.buildingCode,
        floorCode: row.floorCode,
        isCommon: row.isCommon,
        suffix: row.suffix,
        desc: row.desc,
        remark: row.remark
      };

      const sceneData = {
        id: sceneResponse.data.id,
        name: sceneResponse.data.name,
        code: sceneResponse.data.code,
        objCode: sceneResponse.data.objCode,
        objUrl: sceneResponse.data.objUrl,
        modelCode: sceneResponse.data.modelCode,
        factoryCode: sceneResponse.data.factoryCode,
        regionCode: sceneResponse.data.regionCode,
        buildingCode: sceneResponse.data.buildingCode,
        floorCode: sceneResponse.data.floorCode,
        remark: sceneResponse.data.remark
      };

      // 构建3D编辑器的URL参数，包含模型和场景数据
      const editParams = new URLSearchParams({
        modelId: row.id,
        modelName: row.name || '',
        objectUrl: row.objectUrl || '',
        objCode: row.objCode || '',
        sceneJsonUrl: sceneJsonUrl, // 添加场景JSON文件路径
        loadScene: 'true', // 标识需要加载场景
        modelData: encodeURIComponent(JSON.stringify(modelData)), // 传递模型数据
        sceneData: encodeURIComponent(JSON.stringify(sceneData)), // 传递场景数据
        editMode: 'true' // 标识为编辑模式
      });

      // 在新标签页中打开全屏3D编辑器
      const editorUrl = `${window.location.origin}/3d/editor/fullscreen?${editParams.toString()}`;
      window.open(editorUrl, '_blank');
    } else {
      // 如果没有找到场景文件，使用当前点击数据的objectUrl作为原始obj文件
      console.warn('未找到场景文件，使用原始模型编辑模式');

      // 准备传递给编辑器的模型数据
      const modelData = {
        id: row.id,
        name: row.name,
        code: row.code,
        objCode: row.objCode,
        objectUrl: row.objectUrl,
        modelImg: row.modelImg,
        factoryCode: row.factoryCode,
        regionCode: row.regionCode,
        buildingCode: row.buildingCode,
        floorCode: row.floorCode,
        isCommon: row.isCommon,
        suffix: row.suffix,
        desc: row.desc,
        remark: row.remark
      };

      // 检查objectUrl是否已经包含完整路径，避免重复拼接
      let objectUrlWithPrefix = '';
      if (row.objectUrl) {
        if (row.objectUrl.startsWith('http://') || row.objectUrl.startsWith('https://')) {
          // 如果已经是完整URL，直接使用
          objectUrlWithPrefix = row.objectUrl;
          console.log('使用完整URL路径:', objectUrlWithPrefix);
        } else {
          // 如果是相对路径，进行拼接
          objectUrlWithPrefix = `http://localhost:81/platform-api/${row.objectUrl}`;
          console.log('拼接相对路径:', objectUrlWithPrefix);
        }
      }
      console.log('=== 原始模型文件URL调试信息 ===');
      console.log('原始row.objectUrl:', row.objectUrl);
      console.log('最终使用的完整URL:', objectUrlWithPrefix);
      console.log('当前行数据:', row);

      // 验证URL是否可访问
      if (objectUrlWithPrefix) {
        console.log('正在验证文件URL是否可访问:', objectUrlWithPrefix);
        fetch(objectUrlWithPrefix, { method: 'HEAD' })
          .then(response => {
            console.log('文件URL验证结果:', response.status, response.statusText);
            if (!response.ok) {
              console.warn('⚠️ 文件URL不可访问，状态码:', response.status);
            }
          })
          .catch(error => {
            console.error('❌ 文件URL验证失败:', error);
          });
      }

      const editParams = new URLSearchParams({
        modelId: row.id,
        modelName: row.name || '',
        objectUrl: objectUrlWithPrefix || '',
        objCode: row.objCode || '',
        modelData: encodeURIComponent(JSON.stringify(modelData)), // 传递模型数据
        editMode: 'true' // 标识为编辑模式
      });

      const editorUrl = `${window.location.origin}/3d/editor/fullscreen?${editParams.toString()}`;
      window.open(editorUrl, '_blank');
    }
  } catch (error) {
    console.error('获取场景数据失败:', error);
    ElMessage.warning('获取场景数据失败，将使用原始模型编辑模式');

    // 准备传递给编辑器的模型数据
    const modelData = {
      id: row.id,
      name: row.name,
      code: row.code,
      objCode: row.objCode,
      objectUrl: row.objectUrl,
      modelImg: row.modelImg,
      factoryCode: row.factoryCode,
      regionCode: row.regionCode,
      buildingCode: row.buildingCode,
      floorCode: row.floorCode,
      isCommon: row.isCommon,
      suffix: row.suffix,
      desc: row.desc,
      remark: row.remark
    };

    // 出错时也检查objectUrl是否已经包含完整路径，避免重复拼接
    let objectUrlWithPrefix = '';
    if (row.objectUrl) {
      if (row.objectUrl.startsWith('http://') || row.objectUrl.startsWith('https://')) {
        // 如果已经是完整URL，直接使用
        objectUrlWithPrefix = row.objectUrl;
        console.log('错误处理 - 使用完整URL路径:', objectUrlWithPrefix);
      } else {
        // 如果是相对路径，进行拼接
        objectUrlWithPrefix = `http://localhost:81/platform-api/${row.objectUrl}`;
        console.log('错误处理 - 拼接相对路径:', objectUrlWithPrefix);
      }
    }
    console.log('错误处理 - 最终使用的模型文件路径:', objectUrlWithPrefix);

    const editParams = new URLSearchParams({
      modelId: row.id,
      modelName: row.name || '',
      objectUrl: objectUrlWithPrefix || '',
      objCode: row.objCode || '',
      modelData: encodeURIComponent(JSON.stringify(modelData)), // 传递模型数据
      editMode: 'true' // 标识为编辑模式
    });

    const editorUrl = `${window.location.origin}/3d/editor/fullscreen?${editParams.toString()}`;
    window.open(editorUrl, '_blank');
  }
}

// 弹窗专用的工厂切换处理
const handleFormFactoryChange = async () => {
  form.value.regionCode = ''
  form.value.buildingCode = ''
  form.value.floorCode = ''
  formRegionOptions.value = []
  formBuildingOptions.value = []
  formFloorOptions.value = []

  if (!form.value.factoryCode) return

  // 通过工厂code找到organizeCode
  const selectedFactory = factoryOptions.value.find(item => item.code === form.value.factoryCode)
  const organizeCode = selectedFactory ? selectedFactory.organizeCode : ''
  if (!organizeCode) return

  try {
    // 用organizeCode请求区域
    const res = await getRegionList({ organizeCode })
    // 添加工厂区选项
    formRegionOptions.value = [
      { code: '0', name: '工厂区' },
      ...(res.data.list || [])
    ]
  } catch (error) {
    console.error('获取区域列表失败:', error)
    ElMessage.error('获取区域列表失败')
  }
}

// 弹窗专用的区域切换处理
const handleFormRegionChange = async () => {
  form.value.buildingCode = ''
  form.value.floorCode = ''
  formBuildingOptions.value = []
  formFloorOptions.value = []

  if (!form.value.regionCode || form.value.regionCode === '0') return

  try {
    // 获取区域详情，包含关联的楼栋和楼层信息
    const res = await getRegion(form.value.regionCode)
    if (res.data) {
      // 获取关联的楼栋信息
      const param = {
        organizeCode: res.data.organizeCode,
        codes: res.data.buildingCodes,
        getFloor: true
      }
      const buildingRes = await findBuildingListWithFloor(param)
      if (buildingRes.data) {
        // 保存区域关联的楼层信息，供楼栋选择时使用
        const regionFloorCodes = res.data.buildingFloorCodes || []
        formBuildingOptions.value = buildingRes.data.map(building => ({
          ...building,
          regionFloorCodes: regionFloorCodes
        }))
      }
    }
  } catch (error) {
    console.error('获取区域关联楼栋楼层失败:', error)
    ElMessage.error('获取区域关联楼栋楼层失败')
  }
}

// 弹窗专用的楼栋切换处理
const handleFormBuildingChange = () => {
  form.value.floorCode = ''
  formFloorOptions.value = []

  if (!form.value.buildingCode) return

  const selectedBuilding = formBuildingOptions.value.find(item => item.code === form.value.buildingCode)
  if (selectedBuilding) {
    // 获取楼栋的所有楼层
    const allFloors = selectedBuilding.children || []
    // 获取区域关联的楼层代码
    const regionFloorCodes = selectedBuilding.regionFloorCodes || []

    // 过滤出区域关联的楼层
    formFloorOptions.value = allFloors.filter(floor => {
      return regionFloorCodes.includes(floor.code)
    })

    if (formFloorOptions.value.length === 0) {
      ElMessage.warning('该楼栋下没有与区域关联的楼层')
    }
  }
}

// 获取工厂列表
const fetchFactoryOptions = async () => {
  const res = await getUserFactory()
  factoryOptions.value = res.data || []
}

// 获取区域列表
const fetchRegionOptions = async () => {
  if (!queryParams.value.factoryCode) {
    regionOptions.value = []
    buildingOptions.value = []
    floorOptions.value = []
    return
  }
  // 通过工厂code找到organizeCode
  const selectedFactory = factoryOptions.value.find(item => item.code === queryParams.value.factoryCode)
  const organizeCode = selectedFactory ? selectedFactory.organizeCode : ''
  if (!organizeCode) {
    regionOptions.value = []
    buildingOptions.value = []
    floorOptions.value = []
    return
  }
  // 用organizeCode请求区域
  const res = await getRegionList({ organizeCode })
  // 添加工厂区选项
  regionOptions.value = [
    { code: '0', name: '工厂区' },
    ...(res.data.list || [])
  ]

  // 加载楼栋列表，只在非工作区的情况下加载
  if (queryParams.value.regionCode !== '0') {
    const buildingRes = await findBuildingListWithFloor({ organizeCode })
    buildingOptions.value = buildingRes.data || []
  }
}

// 工厂切换时加载区域
const handleFactoryChange = () => {
  queryParams.value.regionCode = ''
  queryParams.value.buildingCode = ''
  queryParams.value.floorCode = ''
  fetchRegionOptions()
}

// 区域切换时加载关联的楼栋和楼层
const handleRegionChange = async () => {
  // 保存当前的楼栋和楼层代码
  const currentBuildingCode = queryParams.value.buildingCode
  const currentFloorCode = queryParams.value.floorCode

  // 只在手动切换区域时清空
  if (!queryParams.value.regionCode) {
    queryParams.value.buildingCode = ''
    queryParams.value.floorCode = ''
  }

  buildingOptions.value = []
  floorOptions.value = []

  if (!queryParams.value.regionCode) {
    return
  }

  // 如果选择的是工作区（code为0），则清空楼栋和楼层
  if (queryParams.value.regionCode === '0') {
    queryParams.value.buildingCode = ''
    queryParams.value.floorCode = ''
    return
  }

  try {
    // 获取区域详情，包含关联的楼栋和楼层信息
    const res = await getRegion(queryParams.value.regionCode)
    if (res.data) {
      // 获取关联的楼栋信息
      const param = {
        organizeCode: res.data.organizeCode,
        codes: res.data.buildingCodes,
        getFloor: true
      }
      const buildingRes = await findBuildingListWithFloor(param)
      if (buildingRes.data) {
        // 保存区域关联的楼层信息，供楼栋选择时使用
        const regionFloorCodes = res.data.buildingFloorCodes || []
        buildingOptions.value = buildingRes.data.map(building => ({
          ...building,
          regionFloorCodes: regionFloorCodes // 将区域关联的楼层代码保存到楼栋对象中
        }))

        // 如果是恢复查询参数，保持原有的楼栋和楼层代码
        if (currentBuildingCode) {
          queryParams.value.buildingCode = currentBuildingCode
          handleBuildingChange(currentFloorCode)
        }
      }
    }
  } catch (error) {
    console.error('获取区域关联楼栋楼层失败:', error)
    ElMessage.error('获取区域关联楼栋楼层失败')
  }
}

// 楼栋切换时加载楼层
const handleBuildingChange = (preserveFloorCode) => {
  // 保存当前的楼层代码
  const currentFloorCode = preserveFloorCode || queryParams.value.floorCode

  // 只在手动切换楼栋时清空
  if (!queryParams.value.buildingCode) {
    queryParams.value.floorCode = ''
  }

  floorOptions.value = []

  if (!queryParams.value.buildingCode) {
    return
  }

  const selectedBuilding = buildingOptions.value.find(item => item.code === queryParams.value.buildingCode)
  if (selectedBuilding) {
    // 获取楼栋的所有楼层
    const allFloors = selectedBuilding.children || []
    // 获取区域关联的楼层代码
    const regionFloorCodes = selectedBuilding.regionFloorCodes || []

    // 过滤出区域关联的楼层
    floorOptions.value = allFloors.filter(floor => {
      // 确保楼层代码匹配
      const isMatched = regionFloorCodes.includes(floor.code)
      if (isMatched && currentFloorCode === floor.code) {
        // 如果找到匹配的楼层，保持选中状态
        nextTick(() => {
          queryParams.value.floorCode = floor.code
        })
      }
      return isMatched
    })

    if (floorOptions.value.length === 0) {
      ElMessage.warning('该楼栋下没有与区域关联的楼层')
    }
  }
}

function saveQueryParams() {
  const cache = JSON.parse(sessionStorage.getItem(QUERY_CACHE_KEY) || '{}');
  cache[route.path] = { ...queryParams.value };
  sessionStorage.setItem(QUERY_CACHE_KEY, JSON.stringify(cache));
}

function restoreQueryParams() {
  const cache = JSON.parse(sessionStorage.getItem(QUERY_CACHE_KEY) || '{}');
  if (cache[route.path]) {
    Object.assign(queryParams.value, cache[route.path]);
  }
}

onMounted(async () => {
  await fetchFactoryOptions(); // 先加载工厂列表
  restoreQueryParams(); // 恢复查询参数
  if (queryParams.value.factoryCode) {
    await fetchRegionOptions(); // 加载区域列表
  }
  if (queryParams.value.regionCode) {
    await handleRegionChange(); // 加载楼栋和楼层
  }
  getList(); // 最后加载数据列表
});

watch(() => route.path, restoreQueryParams);
onBeforeUnmount(saveQueryParams);
watch(queryParams, saveQueryParams, { deep: true });

// 添加 activated 钩子，处理组件被激活时的数据刷新
onActivated(() => {
  getList();
});
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.search-bar {
  margin-bottom: 20px;
}

/* 卡片视图样式 */
.card-container {
  margin-top: 20px;
}

.card-item {
  margin-bottom: 20px;
}

.model-card {
  width: 100%;
  transition: all 0.3s ease;
}

.model-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-wrapper {
  width: 100%;
  position: relative;
}

.card-model-container {
  width: 100%;
  height: 200px;
  position: relative;
  overflow: hidden;
  border-radius: 4px;
}

.model-viewer {
  width: 100%;
  height: 200px;
  display: block;
  border-radius: 4px;
}

.card-model-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 4px;
}

.placeholder-text {
  margin-top: 8px;
  color: #c0c4cc;
  font-size: 14px;
}

/* 遮罩层样式 */
.overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  border-radius: 4px;
  flex-wrap: wrap;
  padding: 10px;
}

.flex-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
  min-width: 50px;
}

.flex-box:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.flex-box .el-icon {
  font-size: 18px;
  margin-bottom: 2px;
}

.flex-box div {
  font-size: 11px;
  text-align: center;
}

.card-info {
  padding: 14px;
  width: 100%;
}

.model-name {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 4px;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.model-suffix {
  color: #909399;
  font-size: 12px;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  box-sizing: border-box;
  color: #303133;
}

.card-desc {
  color: #666;
  font-size: 14px;
  margin-bottom: 12px;
  height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.card-info {
  margin-bottom: 12px;
}

.card-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
}

.info-label {
  color: #909399;
  font-weight: 500;
}

.info-value {
  color: #606266;
  font-weight: 600;
}

.card-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.el-card {
  width: 100%;
}

.el-card__body {
  width: 100%;
  padding: 0 !important;
}

/* 确保所有内容都在容器内 */
.el-card,
.el-card__body,
.card-wrapper,
.card-content,
.card-title {
  box-sizing: border-box;
  overflow: hidden;
}
</style>
